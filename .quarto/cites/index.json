{"index.qmd": [], "intro.qmd": ["knuth84"], "parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.qmd": [], "parts/advanced-topics/intro.qmd": [], "parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.qmd": [], "parts/case-studies-and-best-practices/intro.qmd": [], "parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.qmd": [], "parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.qmd": [], "parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.qmd": [], "parts/complex-data-structures/intro.qmd": [], "parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.qmd": [], "parts/concurrent-programming/intro.qmd": [], "parts/concurrent-programming/master-the-art-of-parallelism-in-go.qmd": [], "parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.qmd": [], "parts/error-handling-and-testing/intro.qmd": [], "parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.qmd": [], "parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.qmd": [], "parts/future-proofing-your-go-code/intro.qmd": [], "parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.qmd": [], "parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.qmd": [], "parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.qmd": [], "parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.qmd": [], "parts/go-fundamentals/intro.qmd": [], "parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.qmd": [], "parts/optimization-techniques/intro.qmd": [], "parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.qmd": [], "parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.qmd": [], "parts/real-world-applications/build-a-scalable-web-service-using-go.qmd": [], "parts/real-world-applications/implement-a-distributed-system-with-go.qmd": [], "parts/real-world-applications/intro.qmd": [], "summary.qmd": []}