{"entries": [], "headings": ["why-write-tests-for-your-go-applications", "best-practices-for-writing-effective-tests", "test-frameworks-and-tools", "overview-of-popular-test-frameworks-in-go", "using-gos-built-in-testing-library-testing.t", "third-party-test-frameworks-ginkgo-gomega-and-more", "test-case-structure-and-organization", "writing-effective-test-cases-tips-and-tricks", "using-tags-and-labels-for-better-test-management", "conclusion", "chapter-master-the-art-of-writing-tests-for-go-applications", "testing-data-and-mocks", "understanding-the-role-of-data-in-writing-good-tests", "working-with-mocks-what-are-they-and-how-to-use-them", "best-practices-for-creating-effective-mock-objects", "test-coverage-and-analysis", "what-is-test-coverage-and-why-should-you-care", "using-gos-built-in-testing-library-testing.coverage", "third-party-tools-for-measuring-and-improving-test-coverage", "conclusion-1"], "options": {"chapters": true}}