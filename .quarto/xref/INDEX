{"index.qmd": {"index.html": "30e7176d"}, "intro.qmd": {"intro.html": "3a24d018"}, "parts/go-fundamentals/intro.qmd": {"intro.html": "c2ed56b4"}, "parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.qmd": {"dive-deep-into-go-syntax-variables-types-and-control-structures.html": "908d404b"}, "parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.qmd": {"master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.html": "261a8f07"}, "parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.qmd": {"explore-interfaces-error-handling-and-package-management.html": "0f873eac"}, "parts/concurrent-programming/intro.qmd": {"intro.html": "62fbda4c"}, "parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.qmd": {"unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html": "77ae4aba"}, "parts/concurrent-programming/master-the-art-of-parallelism-in-go.qmd": {"master-the-art-of-parallelism-in-go.html": "b583602b"}, "parts/complex-data-structures/intro.qmd": {"intro.html": "15fd6b8d"}, "parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.qmd": {"explore-advanced-data-structures-such-as-trees-and-graphs.html": "c647bee8"}, "parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.qmd": {"master-the-art-of-sorting-and-searching-complex-data.html": "85b23686"}, "parts/real-world-applications/intro.qmd": {"intro.html": "4c4697ac"}, "parts/real-world-applications/build-a-scalable-web-service-using-go.qmd": {"build-a-scalable-web-service-using-go.html": "22d70efe"}, "parts/real-world-applications/implement-a-distributed-system-with-go.qmd": {"implement-a-distributed-system-with-go.html": "b0f9808d"}, "parts/optimization-techniques/intro.qmd": {"intro.html": "c0e8a16b"}, "parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.qmd": {"learn-best-practices-for-writing-efficient-go-code.html": "055e99a9"}, "parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.qmd": {"master-the-art-of-profiling-and-optimizing-go-applications.html": "b0fd4a26"}, "parts/error-handling-and-testing/intro.qmd": {"intro.html": "1535c1ee"}, "parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.qmd": {"learn-how-to-handle-errors-in-go.html": "ed910462"}, "parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.qmd": {"master-the-art-of-writing-tests-for-go-applications.html": "6da21883"}, "parts/advanced-topics/intro.qmd": {"intro.html": "17e08c45"}, "parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.qmd": {"explore-advanced-topics-in-go-such-as-coroutines-and-fibers.html": "fc8221ce"}, "parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.qmd": {"master-the-art-of-writing-concurrent-code-in-go.html": "26886992"}, "parts/case-studies-and-best-practices/intro.qmd": {"intro.html": "b00100c1"}, "parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.qmd": {"learn-from-real-world-case-studies-of-using-go.html": "cb72c336"}, "parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.qmd": {"master-the-art-of-writing-maintainable-and-scalable-code-in-go.html": "8c361329"}, "parts/future-proofing-your-go-code/intro.qmd": {"intro.html": "fbaa9b60"}, "parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.qmd": {"learn-how-to-write-future-proof-code-in-go.html": "50ea59ed"}, "parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.qmd": {"master-the-art-of-adapting-to-changing-requirements-and-technology.html": "be404c62"}, "summary.qmd": {"summary.html": "0c355882"}}