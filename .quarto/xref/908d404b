{"entries": [], "headings": ["what-is-go", "why-use-go", "setting-up-your-development-environment", "go-syntax-and-basics", "variables-types-and-initialization", "control-structures-ifelse-switch-and-loops", "ifelse-statements", "switch-statements", "loops", "functions-and-closures", "functions", "default-parameter-values", "variable-number-of-arguments", "closures", "go-variables-and-data-types", "integers-and-floating-point-numbers", "strings-and-booleans", "arrays-slices-and-maps", "control-structures-in-go", "ifelse-statements-1", "switch-statements-1", "loops-for-while-and-range", "for-loops", "while-loops", "range-loops", "functions-in-go", "defining-functions", "function-arguments-and-returns", "function-closures-and-higher-order-functions", "key-takeaways"], "options": {"chapters": true}}