{"entries": [], "headings": ["what-are-errors-in-go", "why-error-handling-is-important", "error-handling-strategies", "panics-and-recovering-from-them", "strategies-for-handling-panics", "best-practices", "creating-custom-error-types", "example", "using-custom-error-types", "example-1", "error-type-hierarchy", "example-2", "conclusion", "error-handling-in-go", "error-handling-in-functions-and-methods", "handling-errors-in-function-calls", "handling-errors-in-method-calls", "best-practices-for-error-handling", "error-handling-with-goroutines", "error-handling-in-goroutine-contexts", "communicating-errors-between-goroutines", "goroutine-based-error-handling-strategies", "example-file-handling-in-a-goroutine", "best-practices-for-goroutine-error-handling"], "options": {"chapters": true}}