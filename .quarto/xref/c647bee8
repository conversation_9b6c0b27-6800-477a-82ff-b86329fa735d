{"entries": [], "headings": ["what-are-trees", "why-use-trees-in-programming", "basic-tree-operations", "tree-implementations", "binary-search-tree-bst", "b-tree", "heap-based-trees", "recent-research", "graphs", "what-are-graphs", "types-of-graphs", "basic-graph-operations", "recent-research-1", "conclusion", "advanced-topics-in-trees-and-graphs", "tree-traversal", "in-order-traversal", "example-code", "pre-order-traversal", "example-code-1", "post-order-traversal", "example-code-2", "graph-search-algorithms", "breadth-first-search-bfs", "example-code-3", "depth-first-search-dfs", "example-code-4", "applications-of-graph-search-algorithms", "minimum-spanning-tree-mst", "kruskals-algorithm", "example-code-5", "prims-algorithm", "example-code-6", "applications-of-mst"], "options": {"chapters": true}}