{"entries": [], "headings": ["best-practices", "challenges-and-considerations", "recent-research-and-comparisons", "example-scenarios", "tools-and-libraries", "conclusion", "chapter-master-the-art-of-parallelism-in-go", "work-stealing-and-pools-in-go", "understanding-work-stealing", "creating-a-work-pool-in-go", "best-practices-for-using-work-pools", "real-world-applications-of-parallelism", "using-parallelism-for-data-processing", "parallelizing-algorithms-in-go", "parallelism-in-web-development-with-go", "conclusion-1", "references"], "options": {"chapters": true}}