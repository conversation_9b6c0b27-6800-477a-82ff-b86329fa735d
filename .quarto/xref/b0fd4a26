{"entries": [], "headings": ["profiling-go-applications", "introduction-to-profiling", "using-pprof-for-cpu-and-memory-profiling", "example-using-pprof-to-profile-a-simple-application", "example-output", "visualizing-profile-data-with-gos-built-in-tools", "example-using-go-slice-to-visualize-profile-data", "example-output-simplified", "best-practices-for-writing-profiles", "optimizing-go-applications", "understanding-gos-garbage-collection", "example-configuring-garbage-collection", "avoiding-unnecessary-allocation", "example-restructuring-code-for-memory-efficiency", "using-cgo-to-optimize-performance-critical-code", "example-using-cgo-to-optimize-a-loop", "profiling-and-optimizing-gos-built-in-functions", "example-benchmarking-a-built-in-function", "advanced-profiling-and-optimization-techniques", "using-gos-runtimedebug-package-for-low-level-debugging", "implementing-your-own-custom-profilers", "optimizing-go-applications-with-machine-learning", "best-practices-for-optimizing-large-scale-go-systems"], "options": {"chapters": true}}