{"entries": [], "headings": ["concurrency-fundamentals", "goroutines-the-building-blocks-of-concurrency", "managing-concurrency-with-goroutine-pools-and-waitgroups", "understanding-goroutine-pools", "using-waitgroups-for-safe-concurrency", "best-practices-for-managing-goroutines", "designing-scalable-and-concurrency-friendly-systems-in-go", "key-principles-for-building-scalable-systems", "avoiding-deadlocks-and-livelocks", "example-of-channels-over-shared-memory", "testing-concurrent-code"], "options": {"chapters": true}}