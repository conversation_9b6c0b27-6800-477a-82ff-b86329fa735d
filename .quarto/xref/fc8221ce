{"entries": [], "headings": ["definition", "key-features", "example", "handling-errors-with-coroutines", "error-propagation", "example-1", "what-are-fibers", "definition-1", "key-features-1", "using-fibers-for-cooperative-scheduling", "example-task-scheduler", "best-practices-for-writing-fiber-based-code", "efficiency-considerations", "synchronization", "resource-management", "choosing-between-coroutines-and-fibers"], "options": {"chapters": true}}