{"entries": [], "headings": ["what-is-concurrent-code", "why-write-concurrent-code", "prerequisites-for-writing-concurrent-code", "goroutines-and-channels", "defining-and-starting-goroutines", "understanding-channel-types", "sending-and-receiving-data-on-channels", "concurrent-control-structures", "the-go-statement-running-a-goroutine", "mutexes-synchronizing-access-to-shared-resources", "waitgroups-managing-concurrency-in-your-code", "conclusion", "mastering-concurrent-programming-in-go-best-practices-and-case-studies", "best-practices-for-writing-concurrent-code", "avoiding-deadlocks-and-livelocks", "minimizing-context-switches", "testing-concurrent-code-effectively", "case-studies-in-concurrent-programming", "writing-a-concurrent-web-crawler", "implementing-a-concurrent-database-interface", "building-a-scalable-concurrent-chat-server", "conclusion-1"], "options": {"chapters": true}}