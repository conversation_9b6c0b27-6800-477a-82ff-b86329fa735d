{"title": "Unravel the Power of Go's Concurrency Model with Goroutines and Channels", "markdown": {"headingText": "Unravel the Power of Go's Concurrency Model with Goroutines and Channels", "containsRefs": false, "markdown": "\nGo's concurrency model has revolutionized how developers approach multi-threaded applications by leveraging goroutines and channels for efficient communication and parallelism.\n\n#### Concurrency Fundamentals\n\nConcurrent programming involves executing multiple tasks simultaneously to improve system performance. In Go, this is achieved through its unique model based on goroutines and channels, which simplifies task management without the overhead of traditional threading.\n\nWhy Concurrency Matters in Go\n- **Efficient Resource Utilization:** Go's non-blocking I/O allows applications to handle multiple inputs and outputs concurrently.\n- **Simplified Programming:** The concurrency primitives in Go reduce the complexity of managing threads explicitly.\n- **Scalability:** It enables building scalable systems by dynamically adding goroutines as needed.\n\n#### Goroutines: The Building Blocks of Concurrency\n\nGoroutines are lightweight, standalone functions that can run concurrently with the main function. They form the core of Go's concurrency model.\n\nCreating Goroutines\n```go\n// Example of a goroutine\nfunc greetUser() {\n    fmt.Println(\"Waiting for user to join...\")\n}\n\nfunc main() {\n    func...greetUser()\n    \n    fmt.Println(\"Main function continues...\")\n}\n```\n\nHandling Goroutine Errors\n```go\n// Including error handling in goroutines\nfunc safeGreet(name string, err *error) {\n    if err != nil {\n        return\n    }\n    fmt.Printf(\"%s has joined!\\n\", name)\n}\n\nfunc main() {\n    func...safeGreet(\"Guest\", &fmt.Errorf(\"invalid user\"))\n    \n    // Error handling outside the goroutine can prevent panic\n}\n```\n\nBest Practices for Writing Goroutines\n1. **Avoid Blocking I/O:** Use channels and async primitives like `io Read` to prevent blocking.\n2. **Use Context.Switch(0):** For single goroutine scheduling, this optimizes performance by preventing context switching overhead.\n\n####Channels: A Powerful Tool for Inter-Process Communication\n\nChannels enable communication between goroutines through synchronized send and receive operations, crucial for inter-process messaging.\n\nIntroduction to Channels\n- **Synchronous Communication:** Channels allow goroutines to send and receive values in a blocking manner.\n- **Asynchronous Communication:** Using `close` allows non-blocking behavior once the sender is closed.\n\nSending and Receiving Messages with Channels\n```go\n// Example of sending and receiving\nfunc main() {\n    c := make(chan string, 0)\n\n    // Sender goroutine\n    func sendMsg() {\n        send c \"Hello from Go\"\n    }\n    \n    // Receiver goroutine\n    func recvMsg() {\n        fmt.Println(\"Received:\", <-c)\n    }\n\n    func...sendMsg()\n    func...recvMsg()\n\n    fmt.Println(\"Main function continues...\")\n}\n```\n\nChannel Closures and Errors\n- **Proper Closure:** Always close channels after sending or receiving to avoid issues.\n```go\n// Properly closing a channel\nfunc main() {\n    c := make(chan string, 0)\n    close(c) // Closing the channel\n}\n```\n- **Handling Unread Messages:** Goroutines may leave messages in communication buffers if not properly managed.\n\nExample Use Cases:\n1. **Echo Server:** Accepts concurrent connections and handles each client's request asynchronously.\n2. **Background Tasks:** Schedule multiple goroutines to perform tasks without blocking the main thread.\n3. **Message Subscription:** Efficiently subscribe to messages sent over channels for real-time processing.\n\nBy understanding and effectively using goroutines and channels, developers can build efficient, scalable, and concurrent applications in Go.\n\n\n### Managing Concurrency with Goroutine Pools and WaitGroups\n\n#### Understanding Goroutine Pools\nGoroutine pools are a powerful tool in Go for managing concurrent tasks. They allow you to run multiple goroutines simultaneously, making it easier to handle asynchronous operations without worrying about context switches or resource contention.\n\n**Example of Using Goroutine Pools:**\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\tpool := make(goroutinePool)\n\t\n\t// Define tasks to perform\n\tfunc1() { \n\t\ttime.Sleep(time.Second)\n\t}\n\tfunc2() { \n\t\ttime.Sleep(time.Nanosecond)\n\t}\n\tfunc3() { \n\t\ttime.Sleep(time.Microsecond)\n\t}\n\n\t// Add tasks to the pool\n\tpool.Add(func1, func2, func3)\n\n\t// Run all goroutines in the pool\n\tpool.Run()\n\n\t// Wait for all tasks to complete\n\tfor i := 0; i < len(pool.WaitGroup); i++ {\n\t\ttime.Sleep(time.Nanosecond)\n\t}\n}\n```\nThis example demonstrates how a goroutine pool can handle different tasks with varying delays, showcasing the flexibility of goroutine pools.\n\n**Potential Issues:**\n- **Resource Competition:** Without proper management, concurrent access to shared resources within a pool can lead to performance degradation.\n- **Over-subscription:** Excessive creation of goroutines without balancing their workload can cause resource exhaustion and degraded performance.\n\n#### Using WaitGroups for Safe Concurrency\nWaitgroups provide a mechanism to handle concurrency safely by allowing multiple goroutine slices to access shared resources concurrently but in a way that doesn't block each other indefinitely.\n\n**Example of Using WaitGroups:**\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\twg := &waitGroup{}\n\tdefer wg.Wait()\n\n\t// Adding goroutines to the group\n\twg.Add(func1,\n\t\tfunc2,\n\t\tfunc3)\n\n\t// Waiting for all goroutines in the group to complete\n\twg.Wait()\n}\n```\nIn this example, three goroutine functions are added to a waitgroup. Once any goroutine completes, the others can proceed without waiting indefinitely.\n\n**Example of Concurrent Access:**\n```go\npackage main\n\nimport (\n\t\"io/ioutil\"\n\t\"time\"\n)\n\nfunc readClient(id int) {\n\tdefer func() {\n\t\tif err := os.Stderr.Error(\"Read finished\", id); err != nil {\n\t\t\tpanic(err)\n\t\t}\n\t}()\n\n\tfmt.Printf(\"Reading from client %d...\\n\", id)\n\tdata, _ := io.ReadAll(\"client%d.txt\" % id)\n\tfmt.Println(data)\n}\n\nfunc serveClients(ch *canal) {\n\tdefer ch.Close()\n\n\twg := &waitGroup{}\n\tdefer wg.Wait()\n\n\t// Adding goroutines to the group\n\twg.Add(func() { readClient(1); },\n\tfunc() { readClient(2); },\n\tfunc() { readClient(3); })\n\n\t// Wait for all goroutines in the group to complete\n\twg.Wait()\n}\n```\nThis example uses a waitgroup to ensure that reading from multiple clients doesn't block each other, improving efficiency.\n\n**Best Practices:**\n- **Limit the Number of Goroutines:** Avoid creating an excessive number of goroutines without balancing their workload.\n- **Proper Error Handling:** Always handle errors in goroutine functions to prevent panics and ensure robustness.\n- **Efficient WaitGroups:** Use waitgroups judiciously to avoid unnecessary waits and resource overhead.\n\n### Best Practices for Managing Goroutines\n1. **Use Goroutine Pools Sparingly:** Only create a goroutine pool when you need to run multiple goroutines asynchronously. Be mindful of the number of goroutines created.\n2. **Handle Errors Gracefully:** In goroutine functions, ensure that errors are handled correctly and panics are avoided to maintain program stability.\n3. **Optimize WaitGroups:** Use waitgroups for shared resource access but avoid over-subscription which can lead to deadlocks or performance issues.\n\n### Designing Scalable and Concurrency-Friendly Systems in Go\nScalability is a cornerstone of building robust applications, especially in concurrent environments.\n\n#### Key Principles for Building Scalable Systems\n- **Decoupling with Async/Await:** Use async/await to decouple components, allowing them to operate independently without blocking.\n- **Single Responsibility Principle (SRP):** Each component should have one responsibility, promoting testability and maintainability.\n- **Resource Limits:** Define clear limits on resource allocation to prevent overcommitment in shared memory spaces.\n- **Avoid Indefinite Loops:** Ensure that goroutines do not run indefinitely without making progress towards completion.\n\n#### Avoiding Deadlocks and Livelocks\n**Deadlocks:** A deadlock occurs when two or more processes waiting for each other's resources can't proceed. To avoid deadlocks:\n- Use channels instead of shared memory to communicate between goroutines.\n- Ensure that goroutines have some exclusive resource access to break the deadlock.\n\n**Livelocks:** A livelock is a situation where multiple processes are waiting indefinitely for a condition to become true. To prevent livelocks, ensure that each process makes progress towards termination and has a way out of the loop.\n\n#### Example of Channels Over Shared Memory:\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc ReadFromClient(ch *canal) {\n\tdefer ch.Close()\n\n\tfor name, data := range makeMap(data) { // Simulate large map access\n\t\tif len(name) > 1000 { // Some condition to break the deadlock\n\t\t\treturn\n\t\t}\n\t\tif _, ok := ch.Read(name, data); ok {\n\t\t\tfmt.Printf(\"Received %s from client %d\\n\", name, id)\n\t\t\tbreak\n\t\t}\n\t}\n}\n\nfunc serveClients(ch *canal) {\n\tdefer ch.Close()\n\n\twg := &waitGroup{}\n\tdefer wg.Wait()\n\n\t// Adding goroutine to the group\n\twg.Add(func() { ReadFromClient(ch); })\n\n\t// Wait for all goroutines in the group to complete\n\twg.Wait()\n}\n```\nIn this example, using a channel ensures that each goroutine makes progress and exits once data is read, preventing deadlocks.\n\n### Testing Concurrent Code\nTesting concurrent code requires special considerations due to potential race conditions. Use mocking libraries or run tests at higher concurrency levels while keeping test cases isolated.\n\n**Example of Test Isolation:**\n```go\npackage main\n\nimport (\n\t\"/stretchr/testify\"\n\t\"time\"\n)\n\nfunc TestGoroutinePool(t *testing.T) {\n\tpool := make(goroutinePool, 5)\n\t\n\ttests := []struct {\n\t\tname    string\n\t\twantErr bool\n\t}{\n\t\t{\n\t\t\tname: \"mix of errors and non-errors\",\n\t\t\twantErr: true,\n\t\t},\n\t}\n\n\tfor _, tt := range tests {\n\t\tt.Run(tt.name, func(t *testing.T) {\n\t\t\tif (!tt.wantErr && err := perr(t); err != nil) { \n\t\t\t\tt.Fail()\n\t\t\t\treturn\n\t\t\t}\n\t\t\ttime.Sleep(time.Second)\n\t\t\tif (!tt.wantErr && err := perr(t); err != nil) { \n\t\t\t\tt.Fail()\n\t\t\t\treturn\n\t\t\t}\n\t\t})\n\t}\n}\n```\nThis test case ensures that errors are handled correctly in different scenarios, maintaining test isolation.\n\nBy following these guidelines and best practices, you can effectively manage concurrency in Go, design scalable systems, avoid common pitfalls like deadlocks and livelocks, and write robust tests for concurrent code.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}