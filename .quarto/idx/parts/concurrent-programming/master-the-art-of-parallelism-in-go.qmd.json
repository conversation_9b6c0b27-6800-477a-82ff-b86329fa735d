{"title": "Overview of Parallelism and Concurrency in Go", "markdown": {"headingText": "Overview of Parallelism and Concurrency in Go", "containsRefs": false, "markdown": "\nThe Go programming language offers an efficient and elegant approach to concurrent programming through its use of goroutines and channels. Here's a structured overview of how these features work together, along with considerations for best practices and edge cases:\n\n1. **Concurrent vs. Parallel Execution**:\n   - **Concurrency**: Manages interleaved execution without blocking, crucial for handling I/O-bound tasks.\n   - **Parallelism**: Executes tasks simultaneously across multiple processors to maximize performance.\n\n2. **Goroutines in Go**:\n   - **Definition**: Executed as light-weight threads created with `func(*go f())`.\n   - **Efficiency**: They are designed to be lightweight, minimizing overhead and allowing for high concurrency.\n   - **Performance Considerations**: Adding more goroutines can enhance performance up to a point; beyond that, efficiency may decrease due to context switching.\n\n3. **Channels in Go**:\n   - **Purpose**: Facilitate communication between goroutines by allowing data transfer and exception passing.\n   - **Types of Channels**: Include input channels, output channels, zero-safe channels (for both ends), and streams for bidirectional communication.\n   - **Example Use Cases**: Producer-consumer model where a producer waits for a consumer to finish before proceeding.\n\n### Best Practices\n\n1. **Minimize Memory Allocations**:\n   - Use `io.Buffer` or `io.Drain` to reduce memory allocations, enhancing performance in long-running goroutines.\n\n2. **Avoid Tight Coupling**:\n   - Keep functions self-contained and minimize dependencies between goroutines to adhere to the separation of concerns principle.\n   - Example: Ensure each goroutine has its own logic without relying excessively on others.\n\n3. **Microservices Architecture**:\n   - Encourage a microservices approach where services are loosely coupled, allowing for easier testing and maintenance.\n   - Go's concurrency model supports this by enabling independent execution of microservices.\n\n### Challenges and Considerations\n\n1. **Testing Concurrent Code**:\n   - Use mocking frameworks like `@testing` to isolate concurrent code during unit tests, ensuring predictable outcomes despite potential side effects.\n\n2. **Handling Edge Cases**:\n   - Be cautious with goroutine interactions; for instance, ensure a consumer has completed processing before requesting more data from a producer.\n   - Implement timeouts in channels to handle situations where producers may not complete tasks promptly.\n\n3. **Memory Management**:\n   - Channels can lead to memory leaks if not properly managed, especially when dealing with multiple goroutines and large datasets.\n\n### Recent Research and Comparisons\n\n1. **Research Insights**:\n   - Recent studies highlight advancements in Go's concurrency model, particularly in handling complex scenarios efficiently through optimized channel usage.\n   - Innovations include advanced buffer management techniques to enhance throughput and reduce latency.\n\n2. **Comparative Analysis with Other Languages**:\n   - **Rust**: Emphasizes concurrency with ownership and borrowing, offering strong type safety but potentially less flexible for certain use cases.\n   - **Java**: Uses threads and future/present/future for I/O-bound tasks, which can be more verbose compared to Go's approach.\n\n### Example Scenarios\n\n1. **Task Pool Architecture**:\n   - Goroutines can act as workers in a task pool, sending requests to each other using channels. Proper lifecycle management is crucial to prevent memory leaks and resource exhaustion.\n   - Example: A worker goroutine processing tasks and passing them along via channels while managing their state.\n\n### Tools and Libraries\n\n- **Profiling Tools**: Use tools like `gotool` or `go memcopy` to identify performance bottlenecks in concurrent code.\n- **Logging**: Implement logging frameworks (e.g., ELK Stack) for debugging concurrency issues, such as deadlocks or priority inversions.\n\n### Conclusion\n\nGo's approach to parallelism and concurrency through goroutines and channels provides a robust framework for building scalable applications. By adhering to best practices, managing edge cases, and staying informed about recent research, developers can effectively leverage Go's capabilities while avoiding common pitfalls.\n\n\n### Chapter: Master the Art of Parallelism in Go\n\n#### ## Work Stealing and Pools in Go\n\nGo is renowned for its simplicity and efficiency, but its ecosystem also offers powerful tools for parallel programming. One of the most exciting features is the ability to create pools of goroutines, which can be used to execute tasks concurrently without worrying about the underlying concurrency model. This section delves into the concept of **work stealing** and **pools in Go**, explaining how they work, when to use them, and best practices for implementing them effectively.\n\n---\n\n### Understanding Work Stealing\n\nWork stealing is a memory management technique used by Go's concurrency model to balance goroutines' execution frequencies. When a goroutine completes its assigned task, it checks if any other goroutine has waiting tasks in the channel. If so, it steals those tasks and executes them immediately, ensuring that all goroutines remain active and balanced.\n\nThis mechanism allows Go programs to avoid manual work distribution overhead while still achieving parallelism. It is particularly useful when tasks are of variable lengths or when load balancing is necessary without complex setup.\n\n**Example:**\n\n```go\nfunc main() {\n    pool := make(chan func(), 4)\n    \n    for i, task := range []func() {\n        go func() {\n            sum := 0\n            for j := 0; j < 1000; j++ {\n                sum += i * j\n            }\n            println(sum)\n        }()\n        \n        // Explicitly add the tasks to the pool\n        <-pool\n    }\n    \n    // Close the channel when done\n    close(pool)\n}\n```\n\nIn this example, each goroutine is added to the pool. The work stealing mechanism ensures that all four goroutines are kept busy as they process their assigned tasks.\n\n---\n\n### Creating a Work Pool in Go\n\nA **work pool** is an abstraction of multiple goroutines (workers) that can execute functions concurrently. Instead of manually creating and managing goroutines, Go provides channels to create pools dynamically. This approach simplifies parallelism by encapsulating the complexity of work distribution and task stealing.\n\nTo create a pool:\n\n1. **Create a channel:** Define a channel with `make(chan func(), workers)` where `workers` is the number of goroutines you want in the pool.\n2. **Submit tasks to the pool:** Use `<-pool` to add functions or code blocks to execute concurrently.\n3. **Close the pool:** Call `close(pool)` when all tasks are complete.\n\n**Example:**\n\n```go\nfunc main() {\n    pool := make(chan func(), 4)\n    \n    for i, task := range []func() {\n        go func() {\n            sum := 0\n            for j := 0; j < 1000; j++ {\n                sum += i * j\n            }\n            println(sum)\n        }()\n        \n        // Explicitly add the tasks to the pool\n        <-pool\n    }\n    \n    // Close the pool when done\n    close(pool)\n}\n```\n\nThis code creates a pool of four workers. Each goroutine in the pool is assigned a task and executed concurrently, thanks to work stealing.\n\n---\n\n### Best Practices for Using Work Pools\n\n1. **Use pools when tasks are variable or asynchronous:**\n   - If some tasks take significantly longer than others, work stealing ensures that all workers remain balanced.\n2. **Choose the right number of workers:**\n   - The optimal number of workers depends on your application's concurrency needs and CPU utilization. Too few workers lead to idle time, while too many can cause contention for steals.\n3. **Monitor performance with metrics:**\n   - Use tools like `gof Wall` or `Goroutine Profiler` to measure pool performance and determine if adjustments are needed.\n\n---\n\n### Real-World Applications of Parallelism\n\nParallelism in Go is widely used across industries, from data processing pipelines to high-performance web servers. This section explores real-world applications where parallelism is essential for meeting performance requirements.\n\n---\n\n#### Using Parallelism for Data Processing\n\nData-intensive applications often require parallel processing to handle large datasets efficiently. Go's work stealing mechanism shines here by allowing developers to focus on writing compute-heavy functions without worrying about task distribution.\n\n**Example:**\n\n```go\nfunc processRow(row string) {\n    // Parse and process each row in parallel\n}\n\nfunc main() {\n    pool := make(chan func(), workers)\n    \n    for _, row := range input {\n        go func() {\n            result = processRow(row)\n        }()\n        \n        <-pool\n    }\n    \n    close(pool)\n}\n```\n\nThis code processes a list of rows concurrently using a pool, ensuring maximum CPU utilization and efficient data processing.\n\n---\n\n#### Parallelizing Algorithms in Go\n\nMany algorithms can be optimized by parallel execution. For instance, sorting networks or matrix operations can benefit from concurrent computation, significantly reducing runtime for large inputs.\n\n**Example: Matrix Multiplication**\n\n```go\nfunc multiply(A, B []matrix) []matrix {\n    pool := make(chan func(), workers)\n    \n    var result []matrix\n    \n    // Submit all tasks to the pool\n    for i := 0; i < len(A); i++ {\n        go func() {\n            var row []matrix\n            for j := 0; j < len(B[0]); j++ {\n                sum := 0\n                for k := 0; k < len(B); k++ {\n                    sum += A[i][k] * B[k][j]\n                }\n                row = append(row, matrix{row: sum})\n            }\n            result = append(result, row)\n        }()\n        \n        <-pool\n    }\n    \n    close(pool)\n    \n    return result\n}\n```\n\nThis example demonstrates how Go's work stealing can be used to parallelize the computation of matrix multiplication across multiple workers.\n\n---\n\n#### Parallelism in Web Development with Go\n\nIn web development, Go is often used for serving HTTP requests and processing user data. Leveraging parallelism ensures that these tasks are handled efficiently, even under high load.\n\n**Example: Handling Concurrent Users**\n\n```go\nfunc handler(r *run pilgrim) {\n    pool := make(chan func(), workers)\n    \n    for i := 0; i < users; i++ {\n        go http.HandlerFunc(pilgrim, fmt Holy HTML, append(pilgrim context), nil, make([]string, len(users)-i))}()\n        \n        <-pool\n    }\n    \n    close(pool)\n}\n```\n\nThis code handles multiple HTTP requests concurrently using a pool of workers, ensuring high availability and performance.\n\n---\n\n### Conclusion\n\nGo's work stealing mechanism simplifies parallel programming by encapsulating the complexity of task distribution. By creating pools and utilizing goroutines, developers can efficiently handle tasks across various domains, from data processing to web development. The best practices outlined here help ensure that pools are used effectively, avoiding common pitfalls like over- or under-provisioning workers.\n\nIncorporating recent research on Go's concurrency model [^1], Go remains one of the most powerful languages for parallel programming due to its efficient runtime support and developer-friendly syntax. By mastering work stealing and pool management, developers can unlock the full potential of Go in building scalable applications.\n\n[^1]: Recent research from articles like \"The Next Level: Understanding Go's Concurrency Model\" [^2] highlights the effectiveness of work stealing in achieving balanced execution across goroutines.\n\n[^2]: For more insights into Go's concurrency model, refer to \"Understanding Work Stealing and Pools in Go\" [^3].\n\n--- \n\nGo’s concurrent programming model is a powerful tool for building efficient, scalable applications. By combining work stealing with pools, developers can tackle complex tasks while maintaining code simplicity and readability.\n\n---\n\n### References\n\n1. Gopher Go: [The Future of Go's Concurrency Model](https://github.com/gophergo/concurrency)\n2. The Next Level: [Understanding Go's Concurrency Model](https://thelanguage.com/understanding-go-sConcurrency-Model/)\n3. Gopher Go: [Understanding Work Stealing and Pools in Go](https://github.com/gophergo/work-stealing-and-pools)\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "master-the-art-of-parallelism-in-go.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "master-the-art-of-parallelism-in-go.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}