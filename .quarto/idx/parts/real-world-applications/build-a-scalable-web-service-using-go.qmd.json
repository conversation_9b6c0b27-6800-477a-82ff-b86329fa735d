{"title": "Build a Scalable Web Service Using Go", "markdown": {"headingText": "Build a Scalable Web Service Using Go", "containsRefs": false, "markdown": "\nIn this chapter, we will explore how to design and implement a scalable web service using Go. We will delve into the architecture, implementation details, and best practices necessary to build efficient and robust services.\n\n### Understanding Requirements for Scalability\n\nScalability is crucial for modern web applications. To achieve this, consider factors like load balancing, auto-scaling, and handling concurrent requests efficiently. Recent research highlights Go's simplicity and performance in production environments, making it an excellent choice for scalable architectures.\n\n### Defining the Service API\n\nA well-defined service API ensures clarity and compatibility with other systems. This involves specifying endpoints, request/response formats, and acceptable methods. Using Go's net/http package allows us to handle HTTP requests efficiently.\n\n### Choosing a Database Solution\n\nGo offers robust database support through SQLite for in-memory operations and its gORM library for PostgreSQL integration. Recent studies emphasize Go's efficiency in handling large datasets, making it suitable for scalable applications.\n\n### Implementing a RESTful API with Go\n\n#### Using the net/http Package\n\nThe net/http package is fundamental for building HTTP servers and clients in Go. Here's an example of a basic HTTP server:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc ServeHttp() {\n\thttp.HandleFunc(\"GET\", http.HandlerFunc(func(f *http Frame) {\n\t\treturn http.StatusOK(200, \"Hello World\")\n\t}))\n\thttp.ListenAndServe(\"\", \"localhost:8080\")\n}\n```\n\n#### Handling HTTP Requests and Responses\n\nTo handle requests and responses, we can use functions like `CreateHandledServer` for client-side handling. An example of a handler:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc Handle(t *http.HandlerFunc) {\n\t// Process request here\n\thttp)}>sendResponse(200, \"Hello from Go\")\n}\n```\n\n#### Request Routing and Middleware\n\nRouting requests based on paths or parameters can be achieved using middleware. Here's an example:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc ExampleMiddleware(h *http.Handler) {\n\th middle := http.HandlerFunc(func(f *http Frame) {\n\t\tswitch f.Path[0] {\n\t\tcase 'hello':\n\t\t\treturn http.StatusOK(200, \"Hello\")\n\t\tdefault:\n\t\t\treturn http.StatusOK(404, \"Not Found\")\n\t\t}\n\t})\n\treturn middle\n}\n\nhttp.HandleFunc(\"GET\", ExampleMiddleware)\n```\n\n#### Error Handling with Status Codes\n\nProper error handling is essential. Using HTTP status codes ensures messages are clear:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc ErrorHandler(f *http Frame) {\n\thttp》sendError(404, \"Resource not found\")\n}\n```\n\n### Example Code for Common Features\n\n#### Handling Different Content Types with JSON\n\nGo handles JSON natively. Here's an example of sending a JSON object:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc sendData() {\n\treq, _ := http》NewRequest(\"GET\", \"data/{id}\", \"application/json\")\n\tjson, err := os.Args[\"data_id\"], nil\n\tif err != nil {\n\t\treturn err\n\t}\n\tjson_val, err := json.Unmarshal(json)\n\tif err != nil {\n\t\treturn err\n\t}\n\thttp》send(json_val)\n}\n```\n\n#### Middleware Composition\n\nCombining middlewares can enhance functionality:\n\n```go\npackage main\n\nimport (\n\t\"net/http\"\n)\n\nfunc LogMiddleware(h *http.Handler) http.HandlerFunc {\n\tlogClient, _ := make(chan error, 10), make(chan error, 10)\n\thandler := h\n\tfor i := range logClient.range(10) {\n\t\thandler = handler.HandlerFunc(func(f *http Frame) {\n\t\t\t// Log request details\n\t\t\treturn http.HandlerFunc(func(f *http Frame) {\n\t\t\t\t// More logging or other processing\n\t\t\t\treturn handler\n\t\t\t})\n\t\t})\n\t}\n\treturn handler\n}\n\nhttp.HandleFunc(\"GET\", LogMiddleware)\n```\n\n#### Rate Limiting\n\nLimiting requests per client can prevent abuse:\n\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc rateLimit(numerator, denominator int) (int, bool) {\n    if numerator > 0 && denominator > 0 {\n        limiter := make(chan int, denominator)\n        var count int = 0\n\n        func increment() {\n            nonlocal count\n            count++\n            if count >= limiter <-1 + count {\n                return\n            }\n        }\n\n        for i := range limiter {\n            increment()\n        }\n    }\n    return numerator, count < denominator\n}\n\nhttp.HandleFunc(\"GET\", func(t *http.HandlerFunc) {\n    requestCount, ok := rateLimit(5, 3)\n    if !ok || t.ClientIP != \"\" {\n        http》send(NO_CONTENT, \"You've been rate limited\")\n    }\n})\n```\n\n#### Security Practices\n\nUsing tokens and enforcing HTTPS are crucial:\n\n```go\npackage main\n\nimport (\n\t\"crypto/elliptic\"\n)\n\nfunc generateToken() *crypto/elliptic.EllipticElement {\n\treturn elliptic.NewCurve25519().GenerateKeyPair()\n}\n\nfunc getToken() string {\n\ttoken := &generateToken().PublicKey().String()\n\treturn token\n}\n```\n\n### Conclusion\n\nBy following these guidelines and best practices, you can build scalable web services using Go. Combining these elements ensures robustness, efficiency, and maintainability in your applications.\n\n\n# Building a Scalable Web Service Using Go\n\n## Load Balancing and Clustering\n\nLoad balancing is crucial for distributing traffic across multiple nodes to prevent overloading any single server. In Go, we can achieve this efficiently using goroutines alongside Echo or Zerodium for load balancing. Here's an example:\n\n```go\nimport (\n\t\"time\"\n)\n\nfunc main() {\n served := make([]struct{ ID int; Handler func() { time.Sleep(300 * time.Second); panic(\"task done\"); } }, 4 )\n\n\tfor i := range served {\n\t\tgo served[i]()\n\t}\n\n EchoMain(\"EchoMain\")\n}\n```\n\nKubernetes can automate this process by managing worker pods to handle load balancing, ensuring scalability in production environments.\n\n## Caching and Content Delivery\n\nCaching reduces load by storing data closer to clients. Redis integration using the `gredis` package is a straightforward solution:\n\n```go\npackage main\n\nimport (\n\t\"gredis\"\n\t\"time\"\n)\n\nfunc main() {\n\t// Create a Redis server on port 6379, key \"app\"\n\tgredis.NewInit(6379, \"app\")\n\n\t// Example: Cache a response\n\tresult := \"Sample response from the server\"\n\tgredis.Set(\"cache.key\", result)\n\t\n\t// When serving HTTP requests\n\thttp.HandleFunc(\"GET /\", func(w, s http.ResponseWriter) {\n\t\tif cached, ok := gredis.Get(\"cache.key\"); ok {\n\t\t\tw.WriteHeader(content: cached)\n\t\t} else {\n\t\t\t// Fetch data from server and save to cache\n\t\t\t_, err := wbee.Sent()\n\t\t\tif err != nil {\n\t\t\t\tgredis.Set(\"cache.key\", err.Error())\n\t\t\t}\n\t\t}\n\t})\n\n\t// Cleanup\n\tgredis.NewInit(0, \"\")\n}\n```\n\nCDNs like Cloudflare offer optimized content delivery, enhancing user experience and reducing load on backend servers.\n\n## Scaling Database Storage\n\nIn-memory databases handle high traffic without disk I/O. LevelDB offers compatibility with Go via `http.google.com/v2Authentication`:\n\n```go\npackage main\n\nimport (\n\t\"bytes\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"github.com/google/leveldb/v2\"\n)\n\nfunc main() {\n\tdb, err := v2.New leveldb.New DB()\n\tif err != nil {\n\t\tfmt.Printf(\"Error creating LevelDB: %v\\n\", err)\n\t\treturn\n\t}\n\n\t// Example: Writing a key-value pair\n\twriteKey := bytes.NewBuffer().\n\t\tWriteBytes([]byte{0x55, 0x12})\n\twriteVal := bytes.NewBuffer().\n\t\tWriteString(\"Sample value\")\n\n\tdbPut, err := db Put writeKey, writeVal, \"test_key\"\n\tif err != nil {\n\t\tfmt.Printf(\"Error putting data: %v\\n\", err)\n\t\treturn\n\t}\n\n\t// Reading data\n\treadKey, readVal, err := db Get &writeKey, \"test_key\"\n\tif err != nil {\n\t\tfmt.Printf(\"Error getting data: %v\\n\", err)\n\t\treturn\n\t}\n\n\tif readVal, ok := readVal.(string); ok {\n\t\tfmt.Println(\"Stored value:\", readVal)\n\t} else if readVal, ok := readVal.(byte[]); ok {\n\t\tfmt.Println(\"Stored bytes:\", string(readVal))\n\t}\n\n\tdb.Close()\n}\n```\n\n## Security: Authentication and Authorization\n\nImplementing OAuth 2.0 with extended capabilities requires additional libraries like `goAuth` for secure authentication:\n\n```go\npackage main\n\nimport (\n\t\"curl\"\n\t\"digest\"\n\t\"fmt\"\n\t\"https\"\n\n\t\"github.com/square/goauth\"\n)\n\nfunc main() {\n\tclient ID := \"client_id\"\n\tclient Secret := \"client_secret\"\n\n\tauth, err := goAuth.NewOAuth2(clientID: client ID, secret: client Secret)\n\n\tif auth nil {\n\t\tfmt.Printf(\"Error initializing OAuth: %v\\n\", err)\n\t\treturn\n\t}\n\n\t// Example: Token request\n\turl := `https://auth server . json`\n\n\tbody, err := curl.New body: url, headers: map[string]string{\n\t\t\"Content-Type\": \"application/x-www-form-urlencoded\",\n\t\t\"grant_type\":    \"authorization_code\",\n\t\t\"is_client\":     true,\n\t}\n\n/--body:access_token code&code=12345)\n\n\tif err != nil {\n\t\tfmt.Printf(\"Error setting up request: %v\\n\", err)\n\t\treturn\n\t}\n\n\tresp, err := auth Post body\n\n\tif err != nil {\n\t\tfmt.Printf(\"OAuth error: %v\\n\", err)\n\t\treturn\n\t}\n\n\t// Handling token response\n\tjsonResp, err := https.NewRequest(\"POST\", url, respHeaders: resp_HEADERS)\n\tif jsonResp nil || err != nil {\n\t\tfmt.Printf(\"Error building request: %v\\n\", err)\n\t\treturn\n\t}\n\n/--body:access_token code&code=12345)\n\n\trespBody, err := jsonReq(jsonResp, \"response\")\n\tif respBody nil || err != nil {\n\t\tfmt.Printf(\"JSON response error: %v\\n\", err)\n\t\treturn\n\t}\n\n\tif jsonErr := jsonResp.NewError; jsonErr != nil {\n\t\tfmt.Printf(\"JSON parsing error: %v\\n\", jsonErr.Error())\n\t}\n\n\tif token, err := jsonBody[\"access_token\"].(string); ok {\n\t\tfmt.Println(\"Access Token:\", token)\n\t} else {\n\t\tfmt.Println(\"No access token in response\")\n\t}\n}\n\n```\n\n## Data Encryption and Validation\n\nUsing Salsa20 for encryption ensures efficient data handling. Here's an example:\n\n```go\npackage main\n\nimport (\n\t\"crypto/d.digest\n\t\"encoding/json\"\n\t\"fmt\"\n\n\t\"github.com/google/leveldb/v2\"\n)\n\nfunc main() {\n\t// Example: Encrypting a string\n\tinput := \"secret message\"\n\tkey := d.NewKey(\"encryption key\").Bytes()\n\n\tencrypted, err := crypto.DSalsa20Encrypt(key, input)\n\tif err != nil {\n\t\tfmt.Printf(\"Encryption failed: %v\\n\", err)\n\t\treturn\n\t}\n\n\t// Decrypting\n\tdecrypted, err := crypto.DSalsa20Decrypt(key, encrypted)\n\tif err != nil {\n\t\tfmt.Printf(\"Decryption failed: %v\\n\", err)\n\t\treturn\n\t}\n\n\tfmt.Println(\"Encrypted:\", encrypted)\n\tfmt.Println(\"Decrypted:\", decrypted)\n}\n```\n\nSecurity best practices include validating inputs and securely handling sensitive data.\n\n## Testing and Debugging\n\nUnit tests validate individual functions:\n\n```go\npackage main\n\nimport (\n\t\"testing\"\n\t\"time\"\n)\n\nfunc TestLoadBalancing(t *testing.T) {\n\tserved := make(chan func(), 4)\n\n\tfor i, f := range served {\n\t\tt.Run(`test function`, fmt.Sprintf(\"test %d\", i), f)\n\t}\n\n EchoMain(\"EchoMain\")\n}\n```\n\nIntegration tests using `curl` or Postman ensure service interactions:\n\n```go\npackage main\n\nimport (\n\t\"curl\"\n\t\"testing\"\n\n\t\"github.com/stretchr/testify/curlTestClient\"\n)\n\nfunc TestLoadBalancing(t *testing.T) {\n\tcurlTestClient.NewClient()\n\tcurlTestClient.Run(\"GET\", \"http://localhost:8080\")\n}\n```\n\nPerformance benchmarks measure scalability:\n\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\tstart := time.Now()\n\n\t// Simulate high traffic with multiple goroutines\n\tfor i := 0; i < 1e6; i++ {\n\t\t_, err := EchoMain(\"EchoMain\")\n\t\tif err != nil {\n\t\t\ttime.Sleep(1 * time.Millisecond)\n\t\t\tcontinue\n\t\t}\n\t}\n\n\tfmt.Println(\"Test completed in:\", time.Since(start))\n}\n```\n\nDebugging techniques include profiling and using tools like `lighthouse` to analyze browser states during tests.\n\nBy integrating these practices, you can build a robust, scalable web service in Go.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "build-a-scalable-web-service-using-go.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "build-a-scalable-web-service-using-go.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}