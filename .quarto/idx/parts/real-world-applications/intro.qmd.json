{"title": "Real-World Applications", "markdown": {"headingText": "Real-World Applications", "containsRefs": false, "markdown": "", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "intro.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "intro.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}