{"title": "Learn How to Write Future-Proof Code in Go", "markdown": {"headingText": "Learn How to Write Future-Proof Code in Go", "containsRefs": false, "markdown": "\nWriting future-proof code is essential for ensuring that your software remains robust, maintainable, and adaptable as technology evolves. In an ever-changing digital landscape, code that becomes obsolete within a few years can be a significant drain on resources and effort. By adopting best practices for writing future-proof code, you can create solutions that are resilient to technological advancements and organizational shifts.\n\nThis section delves into the principles and practices of crafting future-proof Go code, ensuring your projects remain viable for as long as possible.\n\n---\n\n## Writing Robust and Maintainable Code\n\nBefore diving into future-proofing, it's important to establish a foundation of robustness and maintainability. Robust code is less likely to break when changes are made or new features are added, while maintainable code can be easily understood, modified, and improved over time.\n\n### Key Characteristics of Robust and Maintainable Code\n\n1. **Readable and Understandable**: Use clear naming conventions, add comments where necessary, and structure your code in a logical flow.\n2. **Separation of Concerns**: Break down complex tasks into smaller, focused functions or methods to improve clarity and reusability.\n3. **Defensive Programming**: Anticipate potential issues and implement safeguards against invalid input or unexpected behavior.\n4. **Testing**: Write unit tests for individual components to ensure they function as intended under various scenarios.\n\n### Example Code: Robust and Maintainable Go\n\n```go\n// Function to calculate the average of a slice of integers.\nfunc CalculateAverage(numbers []int) (float64, error) {\n    if len(numbers) == 0 {\n        return 0, errors.New(\"empty slice\")\n    }\n    \n    sum := 0\n    for _, number := range numbers {\n        sum += number\n    }\n    \n    return float64(sum)/float64(len(numbers)), nil\n}\n```\n\nThis function is robust because it handles edge cases like an empty input and uses defensive programming. It's maintainable due to its clean structure and modular design.\n\n---\n\n## What Is Future-Proof Code?\n\nFuture-proof code refers to software that can evolve with technological advancements without requiring significant overhauls. It remains functional, efficient, and scalable for as long as possible, even as new technologies emerge or existing ones become obsolete.\n\n### Why is Future-Proofing Important?\n\nIn a rapidly changing world, the risk of becoming obsolete grows with time. A piece of code that becomes \"brittle\" (i.e., fragile to future changes) can lead to costly rewrites and inefficiencies. By designing for the future, you reduce this risk.\n\n---\n\n## Key Principles of Future-Proof Coding\n\nTo write future-proof code, follow these principles:\n\n### 1. **Reusability**\nDesign your code with extensibility in mind. Components that can be reused across different contexts or projects are more likely to remain relevant long-term.\n\n**Example**: Instead of writing ad-hoc code for a task, create reusable functions or packages.\n\n```go\n// Reusable function to validate email addresses.\nfunc isValidEmail(email string) (bool, error) {\n    // Implementation details...\n}\n\n// Example usage:\nif _, err := isEmailValid(\"<EMAIL>\"); err == nil {\n    // Email is valid\n}\n```\n\n### 2. **Modularity**\nBreak your code into independent modules or packages that can operate semi-autonomously. This makes it easier to modify or replace components without affecting the rest of the system.\n\n**Example**: Use Go's module system to separate concerns.\n\n```go\n// Package main\nfunc main() {\n    // Main logic...\n}\n\n// Package controllers\ntype UserController struct {\n    // Fields and methods...\n}\n```\n\n### 3. **Abstraction**\nRemove unnecessary details from your code by abstracting away low-level complexities. This allows higher-level components to function independently of their underlying implementation.\n\n**Example**: Use interfaces to define the behavior of a type without exposing its internals.\n\n```go\n// Interface for user authentication.\ntype AuthHandler interface {\n    CheckUser() bool\n}\n\n// Concrete implementation using OAuth.\nfunc (h *AuthHandler) CheckUser() bool {\n    // Authentication logic...\n}\n```\n\n### 4. **Evolvability**\nPlan for changes in technology and requirements by incorporating flexibility into your code.\n\n**Example**: Use default parameters or optional arguments to allow components to be customized later.\n\n```go\n// Function that accepts a version parameter.\nfunc ProcessData(data []int, version string) ([]string, error) {\n    if version == \"latest\" {\n        // Latest processing logic...\n    } else {\n        // Alternative processing logic...\n    }\n}\n```\n\n### 5. **Separation of Concerns**\nEnsure that components responsible for different aspects of the application operate independently. This makes it easier to modify or replace one component without affecting others.\n\n**Example**: Use Go's `os.Getenv` function instead of implementing your own environment variables handling.\n\n```go\n// Using Go's built-in environment variable retrieval.\nenv := os.Getenv(\"KEY\")\n\nif env == nil {\n    return \"\", errors.New(\"environment variable not found\")\n}\n\nreturn strings.Join(env, \"\\n\"), nil\n```\n\n---\n\n## Designing for the Future\n\nWriting future-proof code requires careful consideration of potential challenges and proactive planning.\n\n### Understanding the Challenges\n\nSeveral factors can make your code vulnerable to becoming obsolete:\n\n1. **Changing Requirements**: Project requirements may evolve or become less critical over time.\n2. **Technological Advancements**: New tools, languages, or frameworks may emerge that render your code obsolete.\n3. **Legacy Systems**: Integration with existing systems that may become outdated or unsupported.\n\n### Design Principles for Future-Proof Code\n\nTo mitigate these challenges, adopt the following design principles:\n\n1. **Flexible Data Structures**: Use data structures and types that can evolve without requiring major changes to your codebase.\n2. **Layered Architecture**: Structure your application in layers (e.g., controllers, services, infrastructure) to allow individual layers to be replaced or updated independently.\n3. **Encapsulation**: Protect sensitive information and logic within components to minimize their impact if they become obsolete.\n4. **Incremental Evolution**: Design systems for incremental improvement rather than complete overhauls.\n\n**Example of a Layered Architecture**:\n\n```go\n// Controller layer that interacts with the service layer.\nfunc controllerAction(req Request, res *state.Res) {\n    // Obtain data from request and pass to service layer.\n    data := getDataFromRequest(req)\n    \n    // Execute service layer logic.\n    result, err := serviceLayer(data)\n    \n    if err != nil {\n        // Handle error and return appropriate response.\n        return state.NewErrorResponse(err)\n    }\n}\n\n// Service layer that interacts with the infrastructure layer.\nfunc serviceLayer(data interface{}) (interface{}, error) {\n    // Execute core functionality.\n    result, err := handleLogic(data)\n    \n    if err != nil {\n        // Return error to controller for handling.\n        return wrapResultToError(err), err\n    }\n}\n\n// Infrastructure layer that interacts with external services.\nfunc infrastructureLayer(data interface{}) (interface{}, error) {\n    // Fetch data from external service.\n    externalData, err := fetchExternalService(data)\n    \n    if err != nil {\n        return wrapResultToError(err), err\n    }\n\n    // Process the data and return it to the service layer.\n    processedData := processExternalResponse(externalData)\n    return processedData, nil\n}\n```\n\n### Becoming a Better Designer\n\nAs a designer of Go applications, focus on creating systems that are easy to maintain and extend. Continuously learn about emerging technologies while collaborating with cross-functional teams.\n\n**Example of Collaboration in Future-Proofing**:\n\nWhen working on a project, involve your team members in discussions about potential future changes. Encourage them to share ideas for how they might design components to be adaptable to new trends.\n\n---\n\n## Best Practices for Writing Future-Proof Go Code\n\nImplement these best practices to ensure your code remains future-proof:\n\n### 1. **Write Modular and Reusable Code**\n\nModular code is easier to maintain, test, and extend. Use Go's package system and module features to structure your application into independent components.\n\n```go\n// Example of a reusable function in a separate package.\npackage controllers\n\nimport \"go.mod\"\n\nfunc controllerAction(req Request, res *state.Res) {\n    // Obtain data from request.\n    data := getDataFromRequest(req)\n    \n    // Execute service layer logic.\n    result, err := serviceLayer(data)\n    \n    if err != nil {\n        return state.NewErrorResponse(err)\n    }\n}\n```\n\n### 2. **Use Go's Error Handling Mechanisms Effectively**\n\nProper error handling ensures that your application can gracefully handle unexpected situations without crashing or producing incorrect results.\n\n**Example of Robust Error Handling in Go**:\n\n```go\n// Function to check if a file exists.\nfunc CheckFileExists(filename string) ([]byte, error) {\n    err := os.ErrNotExist\n    if _, err = os.ReadFile(filename); err != nil {\n        return make([]byte, 0), err\n    }\n    \n    return nil, err\n}\n```\n\n### 3. **Code Organization and Structure**\n\nOrganize your code into logical directories based on functionality. Use Go's workspace syntax to group related packages.\n\n**Example of Good Code Organization**:\n\n```go\n// src/main/\n//     controllers.go         // Contains controller functions.\n//     services.go           // Contains service logic.\n//     infrastructure.go      // Contains infrastructure components.\n//     main.go                // Main application entry point.\n\n// src/models/\n//     user.go          // Defines the User model.\n//     order.go        // Defines the Order model.\n\n// src/controllers/\n//     controllers.go   // Contains controller functions (see above).\n```\n\n### 4. **Write Tests for Every Component**\n\nAutomated tests ensure that your code behaves as expected under various scenarios and can adapt to future changes.\n\n**Example of a Test in Go**:\n\n```go\npackage controllers\n\nimport (\n    \"testing\"\n    \"time\"\n)\n\nfunc TestControllerAction(t *testing.T) {\n    // Arrange: Create test data.\n    req, _ := http.NewRequest(\"GET\", \"/\")\n    req.Header.Set(\"Content-Type\", \"application/json\")\n    req.Body.WriteString(\"{\" + \"user\": []string{\"name\": \"test\"} + \"}\")\n\n    // Act: Call the controller action.\n    t.Run(\"Test controller action with sample data\", func(t *testing.T) {\n        res, err := controllerAction(req, nil)\n        \n        if err != nil {\n            t.Fatalf(\"Error: %v\", err)\n        }\n    })\n}\n```\n\n### 5. **Keep Documentation**\n\nMaintain clear documentation of your code to ensure that future maintainers and collaborators understand your design decisions.\n\n**Example of Good Documentation in Go**:\n\n```go\n// src/services/\n//     service.go           // Contains service logic.\n//     service.html         // Describes the service's functionality and state.\n```\n\n---\n\n## Conclusion\n\nWriting future-proof code is a skill that requires careful planning, modular design, and continuous learning. By following best practices and adhering to Go's idioms, you can create software that will stand the test of time. In your next project, focus on creating adaptable, maintainable, and scalable solutions that can evolve with technology and organizational needs.\n\nBy understanding the principles of future-proofing and applying them in your work, you contribute to a world where software is as relevant now as it will be in 10 years or more. Happy coding!\n\n\n### Error Handling in Go\n\n#### The Importance of Error Handling\n\nError handling is a cornerstone of writing future-proof code because it allows developers to anticipate and manage unexpected issues gracefully. In an ever-evolving technological landscape, APIs may change, new packages emerge, or external factors can impact functionality. Without proper error handling, these unforeseen challenges could lead to crashes or broken applications. By implementing robust error handling, developers ensure that their code remains resilient and adaptable, reducing the risk of future issues.\n\n#### How to Handle Errors in Go\n\nGo offers a straightforward approach to error handling through its `error` type, specifically designed as a pointer to an interface named `error`. This type is non-nil, meaning it cannot be `nil`, which simplifies error management. Functions that might encounter errors return an `error` pointer using the `return` keyword, allowing callers to check for errors before proceeding.\n\nFor instance, consider a function that reads data from a file:\n\n```go\nfunc readData(filename string) (interface{}, error) {\n    err := os.ReadFile(filename)\n    if err != nil {\n        return nil, err\n    }\n    // ... processing the data ...\n}\n```\n\nIn this example, the `readFile` function returns an error upon failure. The caller can check for non-nil errors immediately after receiving a result or another value.\n\nGo also provides the `recover()` function to handle runtime errors by resuming execution at the nearest normal halt point in the code. This is particularly useful when functions might fail and require recovery steps before terminating.\n\n#### Becoming an Expert at Error Handling\n\nMastering error handling involves several best practices:\n\n1. **Contextual Capture**: Use `context()` to capture surrounding statements when handling errors, providing context for more informative error messages.\n2. **Error Messages**: Ensure that error messages are clear and include relevant details such as the function name, parameters, and a brief description of the issue.\n3. **Consistency**: Maintain uniformity in error representation across functions to facilitate easier debugging and testing.\n\nBy adhering to these principles, developers can enhance code reliability and reduce the likelihood of future issues arising from overlooked errors.\n\n### Testing for the Future\n\n#### The Role of Testing in Writing Future-Proof Code\n\nTesting is vital for creating future-proof code because it helps identify potential issues before they become critical. Through thorough testing, especially regression testing, developers ensure that changes do not break existing functionality. Comprehensive test coverage enhances confidence in the codebase and promotes robustness against unforeseen changes or external influences.\n\n#### Writing Effective Tests for Your Code\n\nEffective tests are crucial for maintaining reliable codebases. In Go, utilizing libraries like testify simplifies writing unit tests with minimal boilerplate. Tests should cover various aspects of a function's behavior, including happy paths, edge cases, and unexpected inputs.\n\nFor example, testing the `readFile` function might involve:\n\n- **Unit Tests**: Verifying that data is read correctly under normal conditions.\n- **Integration Tests**: Ensuring compatibility with other parts of the system or external dependencies.\n- **End-to-End Tests**: Simulating end-user scenarios to test the full flow of application operation.\n\nOrganizing tests in a logical structure, such as separating them by function types and using specific naming conventions, improves maintainability and readability.\n\n#### Test-Driven Development (TDD) and Beyond\n\nTest-Driven Development (TDD) is an effective methodology where tests are written before implementing the corresponding code. This approach ensures that code meets test specifications from the beginning, promoting clarity and reducing ambiguities during development.\n\nBeyond TDD, acceptance testing can be employed when integrating with external systems or APIs, allowing for more flexible testing strategies that focus on meeting specific requirements rather than just passing tests.\n\nBy combining thorough testing practices with advanced methodologies like TDD, developers can craft codebases that are not only reliable but also adaptable to future changes and advancements.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "learn-how-to-write-future-proof-code-in-go.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "learn-how-to-write-future-proof-code-in-go.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}