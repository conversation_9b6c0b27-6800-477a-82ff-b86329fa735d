{"title": "Introduction to Concurrent Programming", "markdown": {"headingText": "Introduction to Concurrent Programming", "containsRefs": false, "markdown": "\n#### What is Concurrent Code?\n\nConcurrent programming refers to the ability of a program to execute multiple tasks or operations simultaneously, allowing for faster execution and improved performance by utilizing shared resources efficiently. In Go, concurrent code leverages **goroutines** (lightweight threads) and **channels** to manage concurrency effectively.\n\nGoroutines are designed to simplify parallelism by enabling functions to run in the background without requiring low-level threading management. They share the same memory space as the main goroutine, making them highly efficient for tasks like file reading/writing, networking, or performing heavy computations.\n\nChannels, on the other hand, provide a communication mechanism between goroutines, allowing them to exchange data and synchronize their execution. Together, goroutines and channels form a powerful abstraction layer for writing concurrent code in Go.\n\n#### Why Write Concurrent Code?\n\nWriting concurrent code is essential for modern applications that need to handle high workloads, such as web servers, databases, or data-intensive applications. With increasing hardware capabilities and the growing complexity of software systems, concurrent programming enables:\n\n1. **Improved Performance**: By parallelizing tasks, concurrent code can significantly reduce execution time.\n2. **Scalability**: Concurrent programs can scale with system resources, handling larger workloads as needed.\n3. **Efficient Resource Utilization**: Concurrent code ensures that shared resources like files, databases, or network interfaces are accessed and used efficiently.\n\n#### Prerequisites for Writing Concurrent Code\n\nBefore diving into concurrent programming in Go, there are several prerequisites to keep in mind:\n\n1. **Understanding goroutines and channels**: A solid grasp of how goroutines and channels work is essential for writing effective concurrent code.\n2. **Knowledge of concurrency control structures**: Familiarity with Go’s built-in tools for managing concurrency, such as `Mutex`, `WaitGroup`, and others, will help you write safe and efficient code.\n3. **Experience with async/await pattern**: While Go’s `go` statement provides an alternative to explicit async/await, understanding the async/await model is still beneficial when working with goroutines and channels.\n\nBy meeting these prerequisites, you’ll be well-equipped to leverage Go’s powerful concurrency model in your projects.\n\n---\n\n### Goroutines and Channels\n\n#### Defining and Starting Goroutines\n\nA **goroutine** is a new thread created by a single goroutine function. You can start multiple goroutines concurrently without using `fork` or `spawn`. For example:\n\n```go\nfunc main() {\n    // Main goroutine\n    println(\"Main: started\")\n\n    // Define a goroutine\n    funcA() {\n        // Function code\n        time.Sleep(0.1)\n        println(\"Goroutine A\")\n    }\n\n    // Start multiple goroutines\n    for i := 0; i < 5; i++ {\n        go funcA()\n    }\n\n    // Main loop continues after starting goroutines\n    time.Sleep(2) // Wait for all goroutines to finish\n\n    // Cleanup code\n    println(\"Main: finished\")\n}\n```\n\nTo start a goroutine, use the `go` keyword followed by a function or closure.\n\n#### Understanding Channel Types\n\nChannels are bidirectional communication structures that allow goroutines to send and receive values. The types of channels include:\n\n1. **Input-Only Channels**: Read-only for sending data.\n2. **Output-Only Channels**: Write-only for receiving data.\n3. **Bidirectional Channels**: Both reading and writing capabilities, synchronized with a `<` lock.\n4. **Zero-Capacity Channels**: Efficient for blocking communication.\n\nHere’s an example of using channels:\n\n```go\n// Send message from main goroutine to child goroutine\nc, err := make(chan string, 0)\nif c == nil {\n    return\n}\n\ns, _ := socket()\nif s == nil {\n    close(ch); // Close the channel if necessary\n    return\n}\n\nchild = os.fork() + 1\n\n// Child goroutine receives a message from the parent\ngo func(name string) {\n    println(\"Received:\", name)\n}.(child, \"Hello from main\")\n```\n\n#### Sending and Receiving Data on Channels\n\nChannels enable goroutines to communicate efficiently. Here’s how to send and receive data:\n\n**Sending:**\n\n```go\ns.send(\"message\") // Send message over the channel\n```\n\n**Receiving:**\n\n```go\nmsg := s.recv()  // Receive a value from the channel\n```\n\nYou can control the flow of communication using `<` (synchronized read), `>` (synchronized write), and `^` (unrestricted read).\n\n---\n\n### Concurrent Control Structures\n\n#### The `go` Statement: Running a Goroutine\n\nThe `go` statement is Go’s primary way to run goroutines. It allows you to execute multiple functions concurrently, each in their own goroutine.\n\nExample:\n\n```go\nfunc main() {\n    // Main goroutine\n    println(\"Main\")\n    func1() { // Function to run as a goroutine\n        time.Sleep(0.5)\n        println(\"Goroutine 1\")\n    }\n    func2() { // Another function to run as a goroutine\n        time.Sleep(0.5)\n        println(\"Goroutine 2\")\n    }\n\n    go func1()\n    go func2()\n\n    // Main loop continues after starting goroutines\n    for i := range <1, 2> { // Wait for both goroutines to finish\n        time.Sleep(0.5)\n    }\n}\n```\n\n#### Mutexes: Synchronizing Access to Shared Resources\n\nA **mutex** is a mechanism that ensures only one goroutine can access a shared resource at a time. Go provides the `Mutex` type in the `sync` package.\n\nExample:\n\n```go\nfunc main() {\n    var lock sync.Mutex\n    x := make(chan int, 0)\n\n    func updateShared() {\n        lock.Lock()\n        // Access shared variable here\n        x <- 1 // Send value to channel\n        lock.Unlock()\n    }\n\n    go updateShared()\n\n    // Read from the channel (only one goroutine can access it)\n    value, _ := x.recv()\n    println(\"Shared resource accessed\", value)\n\n    // Cleanup code\n    lock.Unlock()\n}\n```\n\n#### WaitGroups: Managing Concurrency in Your Code\n\nA **waitgroup** is a way to wait for multiple goroutines to finish before proceeding. Go’s `WaitGroup` type allows you to register and manage waiting goroutines.\n\nExample:\n\n```go\nfunc main() {\n    // Main goroutine\n    func main() {\n        // Perform task A\n        time.Sleep(1)\n        println(\"Task 1\")\n    }\n\n    // Define a waitgroup\n    wg := &waitgroup{}\n    go funcA() { // Function to run as a goroutine\n        time.Sleep(0.5)\n        print(\"Task 2\")\n        wg.Add(funcA)\n    }\n\n    // Start another task and register with the waitgroup\n    go funcB() {\n        time.Sleep(1)\n        print(\"Task 3\")\n        wg.Add(funcB)\n    }\n\n    // Wait for all goroutines to finish\n    wg.Wait()\n\n    // Cleanup code after tasks are complete\n    print(\"Main loop continues\")\n}\n```\n\n---\n\n### Conclusion\n\nBy leveraging **goroutines**, **channels**, and **concurrent control structures** in Go, you can write efficient, scalable, and concurrent programs. These tools simplify parallelism and resource management, allowing you to tackle complex problems with ease.\n\nWith proper use of these concepts, you’ll be able to harness the power of concurrency in Go for your next project!\n\n\n# Mastering Concurrent Programming in Go: Best Practices and Case Studies\n\nConcurrent programming is at the heart of building scalable and performant applications. In Go, leveraging concurrency effectively can significantly enhance application performance by utilizing multiple CPU cores efficiently. This chapter delves into best practices for writing concurrent code in Go, focusing on avoiding deadlocks and livelocks, minimizing context switches, and effective testing strategies.\n\n## Best Practices for Writing Concurrent Code\n\n### Avoiding Deadlocks and Livelocks\n\nDeadlocks occur when two or more concurrent processes wait indefinitely for each other to release resources. To avoid deadlocks in Go:\n\n1. **Understand Dependencies**: Analyze your code's dependencies between goroutines to identify potential deadlock scenarios.\n2. **Use Timeouts**: Implement timeouts on waiting operations using `time.Sleep()`. This allows the program to proceed instead of getting stuck indefinitely.\n3. **Synching Primitives**: Utilize Go's built-in primitives like `sync.WaitGroup`, `Wait`, and `Cancel` for better control over wait states in multi goroutine scenarios.\n\nExample code:\n\n```go\n// deadlockExample demonstrates deadlock prevention using timeouts\n\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc deadlockExample() {\n\t wg := make(chan func(), 3)\n\t\n\tgo func() {\n\t\ttime.Sleep(time.Second)\n\t wg.close()\n\t}()\n\n\tgo func() {\n\t\ttime.Sleep(time.Nanosecond * 1000) // Timeout after a short delay\n\t\twg.close()\n\t}()\n\n\tgo func() {\n\t\ttime.Sleep(time.Nanosecond * 500) // Shorter timeout, may wake up earlier\n\t wg.close()\n\t}()\n\n\t// Cleanup\n\ttime.Sleep(time.Second)\n}\n```\n\n### Minimizing Context Switches\n\nContext switches in Go can be costly due to garbage collection and memory management. To minimize them:\n\n1. **Leverage Garbage Collection**: Use a lightweight GC strategy that doesn't interfere with concurrency.\n2. **Tail Recursion Optimization (TRO)**: Write recursive functions using TRO where possible, as it avoids stack growth and reduces context switches.\n\nExample code illustrating TRO usage:\n\n```go\n// tailRecursionExample shows minimizing context switches using TRO\n\npackage main\n\nfunc main() {\n\t// Using a simple loop to mimic recursion with TRO\n\tfor i := 0; i < 1000000; i++ {\n\t\t// Simulating recursive function calls without actual stack usage\n\t}\n\tprintln(\"Loop completed\")\n}\n```\n\n### Testing Concurrent Code Effectively\n\nTesting concurrent code is challenging due to the single-threaded nature of Go's execution model. Use these strategies:\n\n1. **Mock Frameworks**: Replace production frameworks with mocks to test concurrency patterns.\n2. **Unit Testing Frameworks**: Use Go's testing libraries like `goated` or `testing` for structured tests.\n3. **Isolate Test Cases**: Implement isolated environments in each test case using context switches.\n\nExample code:\n\n```go\n// testConcurrencyExample demonstrates testing concurrent code\n\npackage main\n\nimport (\n\t\"context\"\n\t\"testing\"\n)\n\nfunc TestConcurrentCode(t *testing.T) {\n\tctx := context.Background()\n\tgo t.Run(func(t *testing.T) {\n\t\t// Simulate a long-running operation in a goroutine\n\t\ttime.Sleep(time.Second)\n\t\tprintln(\"Operation completed\")\n\t})\n}\n```\n\n## Case Studies in Concurrent Programming\n\n### Writing a Concurrent Web Crawler\n\nA web crawler uses concurrency to fetch and process multiple URLs simultaneously. Here's an example:\n\n```go\n// webCrawlerExample shows concurrent web crawling\n\npackage main\n\nimport (\n\t\"bytes\"\n\t\"encoding/html\"\n\t\"fmt\"\n\t\"net/parse\"\n\t\"time\"\n)\n\nfunc parseHtml(html string) string {\n\treturn html\n}\n\nfunc webCrawler(baseURL string, maxDepth int) {\n\tctx := context.Background()\n\tgo func() {\n\t\turls := map[string]string{\n\t\t\tbaseURL: baseURL,\n\t\t}\n\t\t\n\t\tfor i := 0; i < maxDepth; i++ {\n\t\t\ttime.Sleep(time.Second)\n\t\t\t// Simulate fetching URLs\n\t\t\tfor _, url := range urls {\n\t\t\t\t// Parse the URL content and add new URLs to next level\n\t\t\t}\n\t\t\tctx.Swap()\n\t\t Delimiter: ctx.Delimiter\n\t\turls = nil // Remove current level after processing\n\t\t}\n\t}\n}\n\nfunc main() {\n\twebCrawler(\"http://example.com\", 3)\n}\n```\n\n### Implementing a Concurrent Database Interface\n\nA concurrent database interface uses channels to handle multiple database operations efficiently:\n\n```go\n// dbInterfaceExample implements a concurrent database interface\n\npackage main\n\nimport (\n\t\"db\"\n\t\"time\"\n)\n\nfunc main() {\n\tctx := context.Background()\n\tgo func() {\n\t\ttime.Sleep(time.Second)\n\t\t// Simulate database operation\n\t}()\n\tgo func() {\n\t\ttime.Sleep(time.Nanosecond * 100) // High concurrency, but safe due to non blocking\n\t\t// Simulate more operations\n\t}()\n}\n```\n\n### Building a Scalable Concurrent Chat Server\n\nA chat server uses queues and channels for efficient message handling:\n\n```go\n// chatServerExample builds a scalable concurrent chat server\n\npackage main\n\nimport (\n\t\"db\"\n\t\"context\"\n\t\"time\"\n)\n\nfunc main() {\n\tctx := context.Background()\n\tgo func() {\n\t\t// Handle incoming messages\n\t}()\n\tgo func() {\n\t\t// Process messages in another goroutine\n\t}()\n}\n```\n\n## Conclusion\n\nBy following these best practices and case studies, developers can effectively leverage Go's concurrency model to build robust, scalable applications. Understanding how to avoid deadlocks, minimize context switches, and write effective tests is crucial for maintaining efficient concurrent code. Additionally, real-world examples like web crawlers, database interfaces, and chat servers demonstrate practical applications of these principles in action.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "master-the-art-of-writing-concurrent-code-in-go.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "master-the-art-of-writing-concurrent-code-in-go.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}