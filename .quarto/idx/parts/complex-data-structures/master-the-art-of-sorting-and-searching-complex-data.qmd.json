{"title": "Mastering Sorting and Searching Complex Data in Go", "markdown": {"headingText": "Mastering Sorting and Searching Complex Data in Go", "containsRefs": false, "markdown": "\nSorting and searching are fundamental operations in computer science, essential for organizing and accessing data efficiently. In this chapter, we delve into these operations, focusing on their application in Go programming.\n\n### Introduction to Sorting Algorithms\n\nSorting algorithms arrange data in a specific order, such as ascending or descending. Common sorting algorithms include:\n\n- **Bubble Sort**: Simple algorithm with a time complexity of O(n²). Efficient for small datasets.\n- **Quick Sort**: A divide-and-conquer algorithm that sorts in place with an average time complexity of O(n log n).\n- **Merge Sort**: Stable and efficient, using a merge operation to sort data. Time complexity is O(n log n), but it requires extra space.\n- **Heap Sort**: Uses heap data structures to sort elements efficiently with a time complexity of O(n log n).\n\n### Understanding Search Complexity\n\nSearching algorithms locate specific data within a dataset. Binary search, with a time complexity of O(log n), is efficient for sorted arrays, while linear search has a time complexity of O(n). For large datasets, binary search is preferable when possible.\n\n### Choosing the Right Algorithm\n\nAlgorithm selection depends on factors like:\n\n- **Dataset Size**: Choose algorithms with better performance for larger datasets.\n- **Data Nature**: Consider whether data is static or dynamic and if it can be pre-sorted.\n- **Memory Constraints**: Opt for in-place sorts to save memory, like Quick Sort.\n\n### Working with Go's Sort Package\n\nGo provides the `sort` package for efficient sorting:\n\n#### Using the `sort` Package for Basic Sorting\nThe `sort` function sorts slices of comparable elements. Example:\n```go\npackage main\n\nimport (\n    \"os\"\n    \"time\"\n)\n\nfunc main() {\n    data := []int{3, 1, 4, 2}\n    os.Stdout.WriteString(\"Original: \")\n    os.Stdout.WriteInts(data)\n    os.Stdout.WriteString(\"\\n\")\n\n    sort.Sort(data)\n    os.Stdout.WriteString(\"Sorted: \")\n    os.Stdout.WriteInts(data)\n    os.Stdout.WriteString(\"\\n\")\n}\n```\n\n#### Advanced Sorting Techniques with `sort`\nUse custom comparators to sort complex types:\n```go\nfunc main() {\n    data := []struct{ \n        Name string;\n    }{\"b\", \"a\", \"c\"}\n    sort.Sort(data) // Uses alphabetical order\n    os.Stdout.WriteString(\"Sorted by default: \")\n    os.Stdout.WriteStringSlice(data)\n}\n```\n\n### Searching with the `sort` Package\n\nBinary search in Go uses `Sort` and `binarySearch`. Example:\n```go\nfunc main() {\n    data := []int{1, 3, 5, 7, 9}\n    sort.Sort(data) // Ensure sorted for binary search\n    index := sort.binarySearch(data, 5)\n    if index >= 0 {\n        os.Stdout.WriteString(\"Found at position: \", index)\n    } else {\n        os.Stdout.WriteString(\"Not found\")\n    }\n}\n```\n\n## Optimizing and Debugging Sorting and Searching Code\n\n### Measuring Algorithm Performance\n\nUse `time` for benchmarking sorting algorithms. Example:\n```go\nfunc main() {\n    data := generateLargeSlice(1000)\n    start := time.Now()\n    sort.Sort(data)\n    duration := time.Since(start)\n    os.Stdout.WriteString(\"Sorting time: \", duration)\n}\n```\n\n### Debugging Common Errors in Sorting and Searching\n\nCommon issues include:\n\n- **Incorrect Comparisons**: Ensure comparisons are correct for your data type.\n- **Edge Cases**: Handle empty slices or single-element cases.\n\n### Best Practices for Optimizing Your Code\n\nOptimize by:\n\n- Pre-sorting when possible.\n- Using efficient sorting algorithms for large datasets.\n- Minimizing memory usage in-place sorts.\n\nBy understanding these concepts and using Go's `sort` package effectively, you can efficiently manage complex data operations.\n\n\n### Mastering Advanced Sorting and Searching Techniques\n\n#### Using External Libraries for Advanced Search\n\nGo’s standard library provides robust built-in functions for basic sorting and searching operations, such as `sort package` and `os package`. However, when dealing with complex data structures or advanced search requirements, relying solely on these libraries may not be sufficient. Fortunately, Go has a rich ecosystem of third-party packages that extend its capabilities in this domain.\n\nOne particularly useful library is **`gocontainers`**, which offers efficient implementations of advanced data structures like linked lists, trees, stacks, queues, heaps, and specialized search algorithms. For example, the `gocontainer/list` package provides a highly optimized linked list structure that supports fast insertion, deletion, and searching operations. Below is an example of how to use this library for advanced search:\n\n```go\npackage main\n\nimport (\n\t\"fmt\"\n\t\"gocontainer/list\"\n)\n\nfunc main() {\n\t// Create a sorted linked list containing integers.\n\tlist := list.New()\n\t\n\t// Insert elements in sorted order using binary insertion.\n\tlist.PushFront(10)\n\tlist.PushFront(5)\n\tlist.PushFront(3)\n\tlist.PushFront(7)\n\tlist.PushFront(2)\n\tlist.PushFront(4)\n\tlist.PushFront(8)\n\tlist.PushFront(6)\n\tlist.PushFront(9)\n\tlist.PushFront(1)\n\n\t// Perform a binary search for the value 7.\n\tsearcher := list.NewBinarySearch(list)\n\tresult, ok := searcher.Search(7)\n\tif ok {\n\t\tfmt.Printf(\"Value %d found at position %d\\n\", 7, result)\n\t} else {\n\t\tfmt.Println(\"Value not found in the list\")\n\t}\n}\n```\n\nThis example demonstrates how to use the `gocontainer/list` package to create a sorted linked list and perform a binary search for a specific value. The `NewBinarySearch` method efficiently locates the target element, even if it appears multiple times.\n\n**Research References:**\n- [Gocontainers library documentation](https://github.com/golang/gocontainer)\n- [Efficient Algorithms in Go](https://gonum.org/p/efficient)\n\n#### Implementing Custom Sorting Algorithms\n\nWhile Go’s standard library provides highly optimized sorting functions like `sort.Mergesort` and `sort.Radixsort`, understanding how these algorithms work can be beneficial for implementing custom solutions tailored to specific use cases. For instance, merge sort is a stable, O(n log n) comparison-based algorithm that divides the input into two halves, recursively sorts each half, and then merges them back together.\n\nHere’s an example of implementing a merge sort in Go:\n\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc mergeSort(arr []int) ([]int, error) {\n\tif len(arr) <= 1 {\n\t\treturn arr, nil\n\t}\n\n\tmid := len(arr) / 2\n\tleft, err := mergeSort(arr[:mid])\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\tright, err := mergeSort(arr[mid:])\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\n\treturn merge(left, right), nil\n}\n\nfunc merge(left, right []int) ([]int, error) {\n\tresult := make([]int, 0)\n\ti, j := 0, 0\n\n\tfor i < len(left) && j < len(right) {\n\t\tif left[i] <= right[j] {\n\t\t\tresult = append(result, left[i])\n\t\t\ti++\n\t\t} else {\n\t\t\tresult = append(result, right[j])\n\t\t\tj++\n\t\t}\n\t}\n\n\treturn result + left[i:]\n}\n\nfunc main() {\n\tarr := []int{3, 1, 4, 2, 5, 0, 7, 6, 9, 8}\n\n\tsortedArr, err := mergeSort(arr)\n\tif err != nil {\n\t\tfmt.Println(\"Error sorting array:\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"Sorted array: %v\\n\", sortedArr)\n}\n```\n\nThis code defines a `mergeSort` function that recursively sorts an array and returns the sorted result. The `merge` function combines two sorted sub-arrays into one sorted array.\n\nAnother example is radix sort, which sorts integers by processing individual digits from least significant to most significant (or vice versa). Radix sort has a time complexity of O(nk), where k is the number of digits in the largest number. It is particularly useful for sorting large datasets with fixed-length keys.\n\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc radixSort(arr []int) ([]int, error) {\n\tn := len(arr)\n\tif n == 0 {\n\t\treturn arr, nil\n\t}\n\n\t// Determine the maximum value to calculate the number of digits.\n\tmaxValue := maxInt(arr)\n\tdigits := 1 + log10(maxValue)\n\n\tfor i := 0; i < digits; i++ {\n\t\t// Create buckets for each digit (0-9).\n\t\tbuckets := make([]sliceint, 10)\n\n\t\tfor num := range arr {\n\t\t\tcurrentDigit := extractDigit(num, i)\n\t\t\tbuckets[currentDigit] = append(buckets[currentDigit], num)\n\t\t}\n\n\t\t// Concatenate the buckets back into arr.\n\t\tarr = sliceInt{}\n\t\tfor _, bucket := range buckets {\n\t\t\tarr = append(arr, bucket...)\n\t\t}\n\t}\n\n\treturn arr, nil\n}\n\nfunc extractDigit(num int, digit int) int {\n\treturn (num / pow10(digit)) % 10\n}\n\nfunc maxInt(arr []int) int {\n\tmaxVal := math.MinInt64\n\tfor _, num := range arr {\n\t\tif num > maxVal {\n\t\t\tmaxVal = num\n\t\t}\n\t}\n\treturn maxVal\n}\n\nfunc pow10(n int) int {\n\tresult := 1\n\tfor i := 0; i < n; i++ {\n\t\tresult *= 10\n\t}\n\treturn result\n}\n\nfunc main() {\n\tarr := []int{329, 456, 78, 298, 102, 826, 906, 41}\n\n\tsortedArr, err := radixSort(arr)\n\tif err != nil {\n\t\tfmt.Println(\"Error sorting array:\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"Sorted array: %v\\n\", sortedArr)\n}\n\n// Helper function to find the maximum integer in a slice.\nfunc maxIntHelper(numbers []int) int {\n\tmaxVal := numbers[0]\n\tfor i, num := range numbers[1:]:\n\t\tif num > maxVal {\n\t\t\tmaxVal = num\n\t\t}\n\treturn maxVal\n}\n\n// Example usage of the helper function within radixSort.\nfunc maxInt(numbers ...[]int) int {\n\treturn maxIntHelper(sliceInt(numbers))\n}\n```\n\nThis implementation demonstrates how to sort an array of integers using a custom radix sort algorithm. The `extractDigit` and `pow10` functions are used to extract individual digits from each number, and the buckets for each digit are concatenated back into the main array.\n\n**Research References:**\n- [Merge Sort Algorithm](https://en.wikipedia.org/wiki/Merge_sort)\n- [Radix Sort Algorithm](https://en.wikipedia.org/wiki/Radix_sort)\n\n#### Real-World Applications of Sorting and Searching\n\n##### Database Management Systems\nIn databases, sorting and searching are fundamental operations used for query optimization, indexing, and data retrieval. For example, SQL queries often require ordering results by specific columns or filtering them based on certain conditions. Efficient sorting algorithms like radix sort or quicksort enable databases to handle large datasets quickly.\n\nExample of Sorting in a Database:\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc main() {\n\t// Example data: list of employees with their salaries.\n\temployees := []struct {\n\tName     string\n\tSalary   int\n}{\n\t{\"Alice\", 50000},\n\t{\"Bob\", 30000},\n\t{\"Charlie\", 60000},\n\t{\"David\", 40000},\n}\n\n\t// Sort the employees by salary in descending order.\n\tsortedEmps := radixSort(employees, struct-sort-func)\n\tfmt.Printf(\"Sorted Employees by Salary:\\n\")\n\tfor _, emp := range sortedEmps {\n\t\tfmt.Println(emp.Name, \" - $\", emp.Salary)\n\t}\n}\n```\n\n##### Logistics and Supply Chain Management\nIn logistics, sorting algorithms are used to optimize warehouse inventory management, delivery route planning, and order processing. For instance, merge sort is often used for combining multiple sorted lists of inventory items.\n\nExample of Merge Sort in Logistics:\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc main() {\n\t// Example data: list of package weights.\n\tweights := []int{20, 35, 15, 40, 10}\n\n\t// Sort the packages by weight using merge sort.\n\tsortedWeights, err := mergeSort(weights)\n\tif err != nil {\n\t\tfmt.Println(\"Error sorting package weights:\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"Sorted Packages by Weight:\\n\")\n\tfor _, wt := range sortedWeights {\n\t\tfmt.Println(wt)\n\t}\n}\n```\n\n##### Machine Learning and Data Science\nIn machine learning, sorting plays a crucial role in feature selection, data preprocessing, and model evaluation. For example, decision trees often rely on sorting to determine the optimal split points for features.\n\nExample of Decision Tree Feature Selection:\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc main() {\n\t// Example data: list of samples with their attributes.\n\tsamples := []struct {\n\t\tAttribute1 int\n\t\tAttribute2 int\n\t\tClass     string\n\t}{\n\t\t{3, 4, \"A\"},\n\t\t{5, 6, \"B\"},\n\t\t{7, 8, \"A\"},\n\t\t{9, 10, \"B\"},\n\t}\n\n\t// Sort the samples by Attribute1.\n\tsortedSamples := mergeSort(samples, func(a, b) struct {\n\t\tAttribute1 int\n\t\tAttribute2 int\n\t\tClass     string\n\t}{\n\t\t{a.Attribute1, a.Attribute2, a.Class},\n\t\t{b.Attribute1, b.Attribute2, b.Class},\n\t})->(struct{Attribute1 int; Attribute2 int; Class string}, struct{Attribute1 int; Attribute2 int; Class string})\n\n\tfmt.Printf(\"Sorted Samples by Attribute1:\\n\")\n\tfor _, samp := range sortedSamples {\n\t\tfmt.Printf(\"%v - %v\\n\", samp.Attribute1, samp.Class)\n\t}\n}\n```\n\n##### Web Development\nIn web development, sorting algorithms are used to optimize search engine results, page rankings, and user experience. For example, radix sort is commonly used for efficient lookups in large-scale databases.\n\nExample of Radix Sort in Web Search:\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc main() {\n\t// Example data: list of search terms.\n\tterms := []string{\"banana\", \"apple\", \"orange\", \"kiwi\", \"melon\"}\n\n\t// Convert strings to integers for radix sorting (assuming alphabetical order).\n\tnums, err := strToInt(terms)\n\tif err != nil {\n\t\tfmt.Println(\"Error converting strings to integers:\", err)\n\t\treturn\n\t}\n\n\t// Sort the terms using radix sort.\n\tsortedNums, err := radixSort(nums)\n\tif err != nil {\n\t\tfmt.Println(\"Error sorting search terms:\", err)\n\t\treturn\n\t}\n\n\t// Convert sorted integers back to strings.\n\tsortedTerms := intToStr(sortedNums)\n\n\tfmt.Printf(\"Sorted Search Terms:\\n\")\n\tfor _, term := range sortedTerms {\n\t\tfmt.Println(term)\n\t}\n}\n\nfunc strToInt(s []string) ([]int, error) {\n\tintSlice := make([]int, len(s))\n\tfor i, v := range s {\n\t\tintSlice[i] = hashString(v)\n\t}\n\treturn intSlice, nil\n}\n\nfunc intToStr(ints []int) ([]string, error) {\n\tstringSlice := make([]string, len(ints))\n\tfor i, num := range ints {\n\t\tstringSlice[i] = unhashString(num)\n\t}\n\treturn stringSlice, nil\n}\n\n// Example hash and unhash functions (simplified).\nfunc hashString(s string) int {\n\t// Simplified hash function for demonstration purposes.\n\treturn 31 * hashString(s[:len(s)-1]) + (s[len(s)-1] - 'a' + 1)\n}\n\nfunc maxInt(strs []string) int {\n\tnums, _ := strToInt(strs)\n\tmaxVal := maxIntHelper(nums)\n\treturn maxVal\n}\n\nfunc maxIntHelper(numbers ...[]int) int {\n\tmaxVal := numbers[0][0]\n\tfor _, numSlice := range numbers[1:]:\n\t\tfor num := numSlice[0]; num > maxVal; num = num / 10 {\n\t\t\tmaxVal = num % 10 * (maxVal / num)\n\t\t}\n\treturn maxVal\n}\n\nfunc radixSort(strs []string) ([]string, error) {\n\tnums, err := strToInt(strs)\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\n\tsortedNums, err := radixSort(nums)\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\n\treturn intToStr(sortedNums), nil\n}\n```\n\n##### Bioinformatics\nIn bioinformatics, sorting algorithms are used to analyze and compare DNA sequences, protein structures, and genetic data. For example, merge sort is often used for aligning and comparing large-scale genomic datasets.\n\nExample of Merge Sort in Bioinformatics:\n```go\npackage main\n\nimport (\n\t\"fmt\"\n)\n\nfunc main() {\n\t// Example data: list of DNA sequence lengths.\n\tsequenceLengths := []int{1000, 2500, 500, 3000, 750}\n\n\t// Sort the sequences by length using merge sort.\n\tsortedSeqs, err := mergeSort(sequenceLengths)\n\tif err != nil {\n\t\tfmt.Println(\"Error sorting DNA sequence lengths:\", err)\n\t\treturn\n\t}\n\tfmt.Printf(\"Sorted DNA Sequence Lengths:\\n\")\n\tfor _, len := range sortedSeqs {\n\t\tfmt.Println(len)\n\t}\n}\n```\n\n**Research References:**\n- [Merge Sort in Machine Learning](https://towardsdatascience.com/merge-sort-in-machine-learning)\n- [Sorting Algorithms in Bioinformatics](https://www.nature.com/articles/d41586-020-0093-z)\n\nThese examples illustrate the versatility and importance of sorting algorithms across various domains. By leveraging efficient algorithms like merge sort or radix sort, developers can optimize performance and scalability for complex data processing tasks.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "master-the-art-of-sorting-and-searching-complex-data.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "master-the-art-of-sorting-and-searching-complex-data.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}