{"title": "5. <PERSON>", "markdown": {"headingText": "5. <PERSON>", "containsRefs": false, "markdown": "\n#### 5.1 What Are Trees?\n\nA tree is a non-linear data structure consisting of nodes connected hierarchically. Each node can have multiple child nodes, but only one parent (except for the root node). This hierarchical structure allows efficient searching and traversal operations.\n\nFor example:\n```\n        A\n       / \\\n      B   C\n     / \\\n    D   E\n```\n\nIn Go code:\n```go\ntype Tree struct {\n\tRoot *Node\n}\n```\n\n#### 5.2 Why Use Trees in Programming?\n\nTrees are essential for organizing data hierarchically, enabling efficient searching and insertion operations with logarithmic time complexity (O(log n)). They are widely used in:\n\n- **File systems**: Representing directory structures.\n- **Databases**: Implementing B-trees for disk-based storage efficiency.\n- **Algorithms**: <PERSON><PERSON><PERSON> coding, decision trees.\n\n#### 5.3 Basic Tree Operations\n\n1. **Traversal Methods**:\n   - Pre-order: Visit node, then left child, then right child.\n   - In-order: Left child, visit node, then right child.\n   - Post-order: Left child, right child, visit node.\n\n2. **Finding Tree Height**: The longest path from root to leaf.\n\n3. **Balanced Trees**: Ensure height difference between subtrees is ≤1 for optimal performance.\n\n4. **Searching Elements**: Efficient in balanced trees (O(log n)) but slower in skewed trees.\n\n5. **Inserting Nodes**: Add nodes based on hierarchical structure.\n\n6. **Deleting Nodes**: Remove from appropriate subtree or replace with child node if necessary.\n\n7. **Iterators**: Allow traversal without recursion, using explicit stack structures.\n\nExample code for a tree traversal:\n```go\nfunc preOrder(node *Node) {\n    if node == nil {\n        return\n    }\n    fmt.Printf(\"Visited %s\\n\", node.Value)\n    preOrder(node.Left)\n    preOrder(node.Right)\n}\n```\n\n#### 5.4 Tree Implementations\n\n##### Binary Search Tree (BST)\n\nA BST is a tree where each left subtree has nodes with smaller values, and right subtrees have larger ones.\n\n- **Insertion**: Compare node value with current node to decide direction.\n```go\nfunc insert(root *Node, value int) *Node {\n    if root == nil {\n        return &Node{Value: value}\n    }\n    if value < root.Value {\n        root.Left = insert(root.Left, value)\n    } else {\n        root.Right = insert(root.Right, value)\n    }\n    return root\n}\n```\n\n- **Deletion**: Remove node based on subtree size or replace with child.\n\n##### B-Tree\n\nA B-tree is a balanced tree structure used in databases for efficient disk operations. Each internal node can have up to 'n' keys and children, reducing I/O operations.\n\nExample code snippet:\n```go\ntype BTreeNode struct {\n\tKeys      []int\n\tChildren   [][]BTreeNode\n\tLeaf       bool\n}\n\nfunc (b *BTreeNode) AddKey(key int) {\n    // Implementation details for insertion logic\n}\n```\n\n##### Heap-Based Trees\n\nA heap is a complete binary tree where parent nodes are ordered with respect to their children. It supports efficient extraction of max or min elements.\n\nExample code:\n```go\ntype MinHeap struct {\n\theap []int\n}\n\nfunc (h *MinHeap) Push(x int) { h.heap = append(h.heap, x) }\nfunc (h *MinHeap) Pop() int {\n    len := len(h.heap)\n    val := h.heap[0]\n    h.heap = h.heap[1:len]\n    return val\n}\n```\n\n#### Recent Research\n\nRecent advancements in tree structures include the development of **Red-Black Trees** for efficient rebalancing and **AVL Trees** for stricter balance, enhancing performance across various applications.\n\n---\n\n### 5. Graphs\n\n#### What Are Graphs?\n\nA graph consists of vertices (nodes) connected by edges, representing complex relationships like social networks or road maps.\n\nFor example:\n```\nA -- B -- C\n|    |    \nD -- E -- F\n```\n\nIn Go code:\n```go\ntype Edge struct {\n    To   int\n}\n\ntype Graph struct {\n    Vertices map[int]Node\n    Edges   []Edge\n}\n```\n\n#### Types of Graphs\n\n- **Directed vs. Undirected**: Edges have direction in directed graphs, but are bidirectional in undirected ones.\n- **Weighted vs. Unweighted**: Edges may carry weights or values.\n- **Cyclic vs. Acyclic**: Cycles exist when a node can reach itself; acyclic graphs avoid this.\n- **Sparse vs. Dense**: Based on the number of edges relative to possible connections.\n\nExample: Social media platforms use undirected, unweighted graphs without cycles (except for mutual connections).\n\n#### Basic Graph Operations\n\n1. **Adding/Removing Vertices/Edges**: Update graph structure accordingly.\n2. **Checking Adjacency**: Determine if an edge exists between two vertices.\n3. **Traversing Graphs**: Use Depth-First Search (DFS) or Breadth-First Search (BFS).\n4. **Shortest Path Finding**: Implement Dijkstra's algorithm for weighted graphs.\n5. **Cycle Detection**: Track visited nodes to prevent revisiting and detect cycles.\n6. **Calculating Properties**: Determine vertex degrees, connected components, etc.\n\nExample code for graph traversal:\n```go\nfunc BFS(graph *Graph, start int) {\n    queue := make([]int, 0)\n    queue = append(queue, start)\n    visited := make(map[int]bool)\n    \n    for len(queue) > 0 {\n        current := queue[0]\n        visited[current] = true\n        \n        if graph.Edges contains edge to other nodes:\n            add them to the queue\n    }\n}\n```\n\n#### Recent Research\n\nResearch in graph algorithms has led to advancements like **Union-Find** data structures for efficient connectivity management and **Topological Sorting** for dependency resolution.\n\n---\n\n### Conclusion\n\nTrees and graphs are fundamental data structures offering unique capabilities for organizing, searching, and traversing complex data relationships. Mastery of these concepts is crucial for developing efficient and scalable applications in Go and beyond.\n\n\n# Advanced Topics in Trees and Graphs\n\n## Tree Traversal\n\nTree traversal refers to the process of visiting each node in a tree exactly once in a specific order. Common tree traversal algorithms include In-order, Pre-order, and Post-order traversals.\n\n### In-order Traversal\nIn-order traversal visits nodes by first traversing the left subtree, then visiting the root node, and finally traversing the right subtree. This method is often used to retrieve data in sorted order for binary search trees.\n\n#### Example Code:\n```go\nfunc InOrderTraverse(node *TreeNode) {\n    if node == nil {\n        return\n    }\n    InOrderTraverse(node.Left)\n    fmt.Printf(\"%v \", node.Value)\n    InOrderTraverse(node.Right)\n}\n```\n\n### Pre-order Traversal\nPre-order traversal visits the root node first, then recursively traverses the left and right subtrees. This method is useful for creating copies of trees or when a node needs to be processed before its children.\n\n#### Example Code:\n```go\nfunc PreOrderTraverse(node *TreeNode) {\n    if node == nil {\n        return\n    }\n    fmt.Printf(\"%v \", node.Value)\n    PreOrderTraverse(node.Left)\n    PreOrderTraverse(node.Right)\n}\n```\n\n### Post-order Traversal\nPost-order traversal visits the left subtree, then the right subtree, and finally the root node. This method is useful for deleting trees or when a node's processing depends on its children.\n\n#### Example Code:\n```go\nfunc PostOrderTraverse(node *TreeNode) {\n    if node == nil {\n        return\n    }\n    PostOrderTraverse(node.Left)\n    PostOrderTraverse(node.Right)\n    fmt.Printf(\"%v \", node.Value)\n}\n```\n\nThese traversal methods are fundamental in various applications, such as parsing expressions or searching for specific data within a tree structure.\n\n## Graph Search Algorithms\n\nGraph search algorithms are used to traverse or search through graph structures. Two of the most common algorithms are Breadth-First Search (BFS) and Depth-First Search (DFS).\n\n### Breadth-First Search (BFS)\nBFS explores all nodes at the present depth before moving on to nodes at the next depth level. It uses a queue data structure and is useful for finding the shortest path in unweighted graphs or determining the connected components of a graph.\n\n#### Example Code:\n```go\nfunc BFS(graph map[Vertex]Edges, startVertex Vertex) {\n    visited := make(map<Vertex]bool)\n    queue := make(Queue)\n\n    enqueue(startVertex)\n    visited[startVertex] = true\n\n    for queue is not empty {\n        current := dequeue(queue)\n        for each edge in graph[current] {\n            neighbor := edge.Destination\n            if !visited[neighbor] {\n                mark as visited\n                enqueue(neighbor)\n            }\n        }\n    }\n}\n```\n\n### Depth-First Search (DFS)\nDFS explores as far as possible along a path before backtracking. It uses a stack data structure and is useful for topological sorting, detecting cycles, or solving puzzles like mazes.\n\n#### Example Code:\n```go\nfunc DFS(graph map[Vertex]Edges, startVertex Vertex) {\n    visited := make(map<Vertex]bool)\n    stack := make(Stack)\n\n    push(startVertex)\n    visited[startVertex] = true\n\n    for stack is not empty {\n        current := pop(stack)\n        for each edge in graph[current] {\n            neighbor := edge.Destination\n            if !visited[neighbor] {\n                mark as visited\n                push(neighbor)\n            }\n        }\n    }\n}\n```\n\n### Applications of Graph Search Algorithms\nGraph search algorithms have numerous applications, including:\n- **Shortest Path Finding**: Used in GPS navigation systems to determine the shortest route between two locations.\n- **Network Routing**: Used in computer networks for routing data packets from one node to another.\n- **Social Network Analysis**: Used to analyze connections and interactions within social networks.\n\n## Minimum Spanning Tree (MST)\n\nA Minimum Spanning Tree is a subset of edges that connects all vertices with the minimum possible total edge weight. It has applications in network design, clustering, and image segmentation.\n\n### Kruskal's Algorithm\nKruskal's algorithm works by sorting all the edges from low to high based on their weights and then adding them one by one to the MST if they don't form a cycle. This process continues until there are (V-1) edges in the MST, where V is the number of vertices.\n\n#### Example Code:\n```go\nfunc KruskalMST(edges []Edge, vertices map[Vertex]struct{}{}) {\n    sortEdges := sort(edge by weight)\n    make(MST as empty graph)\n\n    for each edge in sortEdges {\n        if Find(edge.Source) != Find(edge.Destination) {\n            Union(edge.Source, edge.Destination)\n            AddEdge to MST\n        }\n    }\n\n    return MST\n}\n```\n\n### Prim's Algorithm\nPrim's algorithm starts with an arbitrary vertex and adds the smallest edge that connects a new vertex to the growing MST. This process continues until all vertices are included in the MST.\n\n#### Example Code:\n```go\nfunc PrimsMST(vertices []Vertex) {\n    select startVertex from vertices\n\n    initialize key for each vertex as infinity except startVertex (key = 0)\n    initialize parent map\n\n    while not all vertices added {\n        u := ExtractMinVertex()\n        add u to MST\n        for each neighbor v of u {\n            if key[v] > weight(u, v) {\n                update key[v] and parent[v]\n            }\n        }\n    }\n\n    return MST\n}\n```\n\n### Applications of MST\n- **Network Design**: Used to design cost-effective networks with minimal total edge weights.\n- **Clustering**: Used in hierarchical clustering to group data points efficiently.\n- **Image Segmentation**: Used to partition images into segments based on pixel similarities.\n\n---\n\nThese sections provide a comprehensive overview of advanced tree and graph traversal techniques, as well as the construction of Minimum Spanning Trees. For further reading, you can explore recent research papers on optimized tree traversals and efficient MST algorithms for large-scale applications in Go programming.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "explore-advanced-data-structures-such-as-trees-and-graphs.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "explore-advanced-data-structures-such-as-trees-and-graphs.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}