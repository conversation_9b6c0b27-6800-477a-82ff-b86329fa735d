{"title": "Writing Tests for Go Applications", "markdown": {"headingText": "Writing Tests for Go Applications", "containsRefs": false, "markdown": "\nTesting is an essential part of software development, ensuring that your application behaves as expected under various conditions. In Go, writing tests not only verifies functionality but also helps catch bugs early, improves maintainability, and supports a robust codebase. As Go applications grow in complexity, so does the importance of comprehensive testing strategies.\n\nThis section dives into the fundamentals of writing effective tests for Go applications, covering best practices, test frameworks, and organizing your test suite. By the end, you'll have a solid foundation to start writing reliable and maintainable tests for your own projects.\n\n## Why Write Tests for Your Go Applications?\n\nWriting tests serves multiple purposes in the development lifecycle:\n\n1. **Verification of Functionality**: Tests ensure that individual components or features behave as intended.\n2. **Early Bug Detection**: By testing early, you can identify and fix issues before they become costly to resolve later.\n3. **Improved Maintainability**: Well-structured tests make your codebase easier to understand and maintain by providing clear expectations for each feature.\n4. **Performance Testing**: Go's performance is often a critical factor, with tests helping to identify bottlenecks or regressions introduced during development.\n5. **Security Assurance**: In production environments, testing helps identify vulnerabilities that could be exploited later.\n\nIn short, writing tests is not just about passing automated checks—it’s about building confidence in your application’s reliability and quality.\n\n## Best Practices for Writing Effective Tests\n\nWriting effective tests requires a systematic approach. Here are some best practices to keep in mind:\n\n1. **Start with Unit Tests**: Begin by testing individual functions or methods before integrating them into larger components.\n2. **Cover All Paths**: Ensure that both the code under test and its dependencies (like external APIs, databases, or configuration files) are thoroughly tested across all possible paths.\n3. **Use Mocks for External Dependencies**: If your application relies on external services, mocks allow you to isolate your code from the real world during testing.\n4. **Leverage Go's Built-in Testing Library**: The `testing` package in Go provides a straightforward way to write unit tests and integrate them into your workflow using tools like `go test`.\n5. **Use Third-Party Frameworks When Appropriate**: Tools like Ginkgo, Gomega, or Testify can simplify testing by providing ready-to-use fixtures and reducing boilerplate code.\n6. **Maintain a Good Test-to-Code Ratio**: Avoid writing tests that duplicate the functionality of your code—tests should provide additional value beyond what’s already written.\n\nBy following these best practices, you’ll create tests that are not only effective but also maintainable over time.\n\n## Test Frameworks and Tools\n\n### Overview of Popular Test Frameworks in Go\n\nGo has a rich ecosystem of testing frameworks, each with its own strengths:\n\n1. **Testing Library (gonum.org)**\n   - The `testing` package is part of the standard library and provides basic test suite creation.\n2. **Ginkgo**\n   - A modern, actively maintained testing framework that supports mocking dependencies and writing clean tests.\n3. **Gomega**\n   - Another popular choice for functional testing, Gomega emphasizes readability and maintainability.\n\n### Using Go's Built-in Testing Library: Testing.T\n\nGo’s standard library includes `testing.T`, which is straightforward to use but less feature-rich compared to third-party frameworks like Ginkgo or Gomega.\n\n**Example Code Using `Testing.T`:**\n\n```go\npackage main\n\nimport (\n\t\"testing\"\n)\n\nfunc TestMyFunction(t *testing.T) {\n\ttests := []struct {\n\t\tname     string\n\t\twant     interface{}\n\t\twantVal  interface{}\n\t}{\n\t\t{\n\t\t\tname: \"my function returns the correct value\",\n\t\t\twant: func() interface{} { return \"hello world\" },\n\t\t\twantVal: \"hello world\",\n\t\t},\n\t}\n\n\tfor _, tt := range tests {\n\t\tif tt.name {\n\t\t\tt.Run(tt.name, func(t *testing.T) {\n\t\t\t\tif !tt.want(tt.f()) {\n\t\t\t\t\tt.Errorf(\"returned %v instead of %v\", tt.wantVal, tt.want)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n}\n\nfunc Want(t *testing.T) interface{} {\n\t// This function is called by the test framework to get each test's expected value\n}\n\nfunc WantVal(t *testing.T) interface{} {\n\t// This function returns the expected value for each test case\n\treturn \"hello world\"\n}\n```\n\n### Third-Party Test Frameworks: Ginkgo, Gomega, and More\n\nThird-party frameworks like Ginkgo and Gomega offer more advanced features such as mocking dependencies, writing cleaner test cases, and better documentation.\n\n**Example Code Using Ginkgo:**\n\n```go\npackage main\n\nimport (\n\t\"ginkgo\"\n\t\"testing\"\n)\n\nfunc TestMyFunction(t *testing.T) {\n\tg := ginkgo.New()\n\t\n\ttests := g.NewGroup(\"Test Cases\")\n\t\n\ttests.Add(g New \"Basic Functionality\", func(t *testing.T) {\n\t\tassert.Equal(t, \"hello world\", MyFunction())\n\t})\n}\n\nsuite, _ := g.Run(t)\nsuite.RunAll()\n\nginkgo.Shutdown()\n```\n\nGinkgo simplifies test suite management and provides predefined fixtures for common data types.\n\n## Test Case Structure and Organization\n\nOrganizing your tests is as important as writing them. Here’s how to structure your test suite:\n\n1. **Test Suites**: Group related test cases into a single suite using packages or directory structures.\n2. **Tagging and Filtering**: Use tags in Go files to filter test cases based on priority, coverage goals, or other criteria.\n\n**Example Tagged Test Case:**\n\n```go\npackage main\n\n// Filenames with the tag \"high_priority\" will be included in the test suite\n// if the environment variable GO_TEST SUITES includes this file.\nfunc (t *testing.T) Tags() string {\n\tif os.Mac() {\n\t\treturn \"macOS\"\n\t}\n\treturn \"linux\"\n}\n\nfunc TestMyFunction(t *testing.T) {\n\tassert.Equal(t, 42, MyCounter())\n}\n```\n\n## Writing Effective Test Cases: Tips and Tricks\n\n1. **Start with a Clear Purpose**: Each test case should have a single responsibility.\n2. **Use Mocks for External Dependencies**: This isolates your code from external factors during testing.\n3. **Handle Stateful Applications**: Use `teardown` and `setup` functions to reset the application state before each test.\n4. **Mock Dependencies**: If you’re testing an API call, mock the service to return a predefined response.\n5. **Document Your Tests**: Include comments or documentation within your test cases to explain their purpose.\n\n**Example Test Case with Setup/Teardown:**\n\n```go\npackage main\n\nimport (\n\t\"testing\"\n)\n\nfunc (t *testing.T) Setup() {\n\t// Reset application state before each test\n}\n\nfunc (t *testing.T) TearDown() {\n\t// Cleanup any resources after the test\n}\n\nfunc TestGetUser(t *testing.T) {\n\tif _, err := t.Setup(); err != nil; {\n\t\treturn\n\t}\n\tassert.NoError(t, \"GET /users\")\n\tassert.EqualJSON(t, \"user details\", t biopsy())\n}\n```\n\n## Using Tags and Labels for Better Test Management\n\nTags allow you to categorize test cases based on their purpose or priority. This makes it easier to run specific subsets of your tests.\n\n**Example Tagged Function:**\n\n```go\nfunc (t *testing.T) Tags() string {\n\tif t wants to be prioritized as high, add a tag like \"high_priority\"\n}\n\nfunc TestMyFunction(t *testing.T) {\n\t// ...\n}\n```\n\n## Conclusion\n\nWriting effective tests is crucial for maintaining the quality of your Go applications. By following best practices, using appropriate frameworks, and organizing your test suite effectively, you can ensure that your application is thoroughly tested and reliable.\n\nIncorporate these tips into your workflow and gradually adopt more advanced testing frameworks as your project grows. Remember, testing should be an integral part of your development process, not just a one-time activity before deployment.\n\n\n### Chapter: Master the Art of Writing Tests for Go Applications\n\n#### Testing Data and Mocks\n\n##### Understanding the Role of Data in Writing Good Tests\n\nTesting is a cornerstone of software development, ensuring that your application behaves as expected under various scenarios. In Go, writing effective tests often involves creating test data—specifically designed inputs, configurations, or states—that allow you to validate your code thoroughly. Test data can come from multiple sources: predefined datasets, mocking external dependencies, or dynamically generating values based on certain conditions.\n\nThe importance of test data lies in its ability to cover edge cases and boundary conditions that might not be evident during normal execution. For example, testing with extreme values (e.g., very large integers, empty strings, or null pointers) can reveal potential bugs or unexpected behavior in your code. Additionally, using mock objects allows you to simulate interactions between components of your application without relying on external services or databases.\n\nWriting good test data requires careful planning and attention to detail. It is often referred to as \"test coverage\" because it ensures that different paths through your code are exercised during testing. To write effective test data:\n\n1. **Identify Test Scenarios**: Determine all possible execution paths in your application.\n2. **Select Representative Inputs**: Choose inputs that cover normal cases, edge cases, and error conditions.\n3. **Use Structured Formats**: Store test data in a structured format (e.g., JSON or YAML) for readability and reusability.\n4. **Leverage Tools**: Use tools like Go's `testing` library or mocking frameworks to automate the loading of test data.\n\n##### Working with Mocks: What Are They and How to Use Them\n\nMock objects are placeholders that mimic the behavior of real components in your application. They allow you to isolate specific parts of your code for testing, ensuring that they behave correctly without being influenced by external factors like other modules or services.\n\nIn Go, mocks can be implemented using libraries such as `mock` and `testing`. The `mock` package provides decorators like `Mock`, `Kill`, and `Spy` that allow you to wrap functions and control their execution during tests. For example:\n\n```go\nfunc MyFunc(a *Mock) int {\n    return a.(*func)(io/ioutil.ReadFile(\"path/to/file\"))\n}\n```\n\nUsing mocks effectively requires following best practices, such as:\n\n1. **Injecting Mocks**: Inject mock objects into your test code to replace dependencies.\n2. **Spying on Methods**: Use `Spy` decorators to observe or modify method calls during testing.\n3. **Managing State**: Ensure that mocks maintain the correct state throughout their lifecycle.\n\n##### Best Practices for Creating Effective Mock Objects\n\nCreating effective mock objects involves balancing flexibility and specificity:\n\n1. **Mock Realistic Dependencies**: Replace external dependencies (e.g., APIs, services) with mocks to isolate your code under test.\n2. **Spy Instead of Killing**: Use `Spy` instead of `Kill` to observe method calls without stopping the test.\n3. **Leverage Mocks for Configuration**: Use mocks to test how your application handles different configurations or scenarios.\n\nBy mastering these techniques, you can significantly improve the reliability and robustness of your Go applications through effective testing.\n\n---\n\n#### Test Coverage and Analysis\n\n##### What is Test Coverage, and Why Should You Care?\n\nTest coverage refers to the measure of code execution during automated tests. It quantifies how much of your source code has been tested for functionality. High test coverage ensures that critical parts of your code are thoroughly tested, reducing the risk of regressions and improving maintainability.\n\nIn Go, test coverage is typically measured using tools like `go test` with the `-cover` flag or third-party libraries such as `coverage` (now known as `gotest`). Understanding your test coverage helps you identify gaps in your testing strategy and prioritize which parts of your code need more attention.\n\n##### Using Go's Built-in Testing Library: Testing.Coverage\n\nGo's standard library provides comprehensive testing tools, including the `testing` package and the built-in `cover` tool. The `Testing` subdirectory contains packages like:\n\n- `out` for writing test output to disk.\n- `cover` for collecting coverage information (though this is deprecated in favor of third-party tools).\n- `mock` for mocking dependencies.\n\nTo enable test coverage, you can run:\n\n```bash\ngo test -cover\n```\n\nThe `-cover` flag outputs a coverage report detailing which parts of your code were tested and uncovered. This helps you identify areas that need additional testing or refactoring.\n\n##### Third-Party Tools for Measuring and Improving Test Coverage\n\nWhile Go's built-in testing library is powerful, it may not always meet the needs of more complex projects. Third-party tools have emerged as valuable additions to a developer's testing toolkit:\n\n1. **`coverage`**: Although deprecated, `coverage` (now known as `gotest`) has been widely used for measuring test coverage in Go applications.\n2. **`lgtm`**: A tool that detects potential bugs and inconsistencies in your codebase based on test coverage insights.\n3. **`covd`**: A command-line tool specifically designed to report test coverage statistics from your Go projects.\n\nBy integrating these tools into your workflow, you can gain deeper insights into your code's test coverage and make data-driven decisions about where to focus your testing efforts.\n\n---\n\n### Conclusion\n\nWriting tests is a critical part of the software development process. By leveraging test data and mock objects effectively, you can isolate components of your application and ensure their correct behavior. Additionally, monitoring test coverage allows you to identify gaps in your testing strategy and improve overall code quality. With Go's robust testing framework and a variety of tools available, you can write comprehensive and reliable tests that drive the evolution of your applications.\n\nBy following best practices in test data management, mock usage, and test coverage analysis, you will be well-equipped to ensure the reliability and maintainability of your Go applications.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "master-the-art-of-writing-tests-for-go-applications.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "master-the-art-of-writing-tests-for-go-applications.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}