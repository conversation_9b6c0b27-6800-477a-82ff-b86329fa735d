{"title": "Writing Efficient Go Code", "markdown": {"headingText": "Writing Efficient Go Code", "containsRefs": false, "markdown": "\n#### Understanding the Importance of Performance\nWriting efficient Go code is crucial for building high-performance applications. While Go’s standard library provides excellent functionality out-of-the-box, developers must be mindful of performance optimizations to ensure their programs run efficiently. <PERSON>’s unique combination of simplicity and efficiency makes it a favorite among developers, but achieving optimal performance requires careful consideration of various factors.\n\nGo is compiled into machine code by the GCC compiler suite, which allows for optimizations such as inlining functions, loop unrolling, and constant folding. These optimizations can significantly improve program performance. However, certain constructs—such as unnecessary variable creation, improper use of data structures, or inefficient function calls—can negate these benefits.\n\nUnderstanding the importance of performance is the first step toward writing efficient Go code. This section delves into best practices for optimizing Go programs, covering topics such as memory management, function optimization, and efficient use of data structures.\n\n#### Go's Memory Model and Its Implications\nGo’s memory model is designed to be garbage-collected automatically, which simplifies memory management for developers. However, this automatic memory management can lead to inefficiencies if not managed properly. Understanding how Go manages memory is essential for writing efficient code.\n\nAt its core, Go uses a reference-counting garbage collector that automatically reclaims unused memory when the last reference to an object is removed. This system ensures that programs do not suffer from memory leaks and provides automatic protection against dangling pointers. However, this automatic management can also lead to performance overhead if not used judiciously.\n\nFor example, creating large numbers of small objects or using inefficient data structures like arrays of structs with unnecessary fields can lead to increased memory usage and slower garbage collection. By understanding Go’s memory model, developers can make informed decisions about how to structure their programs for optimal performance.\n\n#### Optimizing Function Calls and Loops\nFunction calls and loops are fundamental constructs in any programming language, and writing them efficiently is critical for performance. In Go, function calls have a small overhead due to the compiler’s optimization of common functions, but this overhead can still add up when called frequently. Similarly, nested or overly complex loops can lead to inefficiencies.\n\nOne area where Go excels is its ability to optimize function calls and loops through various techniques. For example, using closures can sometimes lead to unnecessary overhead, while other constructs like channel primitives can provide more efficient alternatives for certain types of operations.\n\nAdditionally, Go provides tools such as `make` and `chan` that allow developers to write efficient code without sacrificing readability or performance. By understanding these optimizations, developers can craft code that is both correct and efficient.\n\n#### Best Practices for Variable Declaration and Usage\nVariables are a fundamental part of any programming language, but writing them efficiently requires careful consideration. In Go, variables must be declared before they are used, and each variable has an associated lifetime. Efficient variable usage ensures that programs run quickly and with minimal memory overhead.\n\nOne of the most important aspects of variable declaration is minimizing unnecessary creation. Creating a new variable for every small change in a loop or function can lead to significant performance overhead. Instead, developers should reuse variables where possible or use temporary variables only when necessary.\n\nAnother consideration is using type inference correctly. Go’s type inference engine can automatically determine the types of variables based on their usage, reducing the need for explicit type annotations. However, over-relying on type inference can sometimes lead to inefficient allocations if not used carefully.\n\nFinally, avoiding global variables and polluting the program’s namespace is a best practice that aligns with Go’s philosophy of keeping things simple and predictable. Global variables are inherently unstable in terms of performance and memory usage because they require garbage collection when no longer in use. Instead, developers should use local variables or other data structures to encapsulate data within their programs.\n\n#### Minimizing Unnecessary Variable Creation\nMinimizing unnecessary variable creation is a key aspect of writing efficient Go code. Inefficient variable creation can lead to increased memory usage and performance overhead. Developers must be mindful of when and how they create variables, especially in loops and function calls where repeated allocations can add up quickly.\n\nOne way to minimize unnecessary variable creation is to reuse variables whenever possible. For example, instead of creating a new integer variable for each iteration of a loop, developers can declare the variable outside the loop and update its value within the loop body. This approach reduces the overhead of variable creation and improves performance.\n\nAnother optimization is to use constants when possible. Constants are declared with an explicit `const` declaration and have no lifetime, which means they cannot be garbage collected. Using constants for values that do not change can improve performance by avoiding unnecessary allocations and reducing cache invalidation times.\n\nAdditionally, developers should avoid creating temporary variables for small changes within expressions. Instead, they can use Go’s concise syntax or other constructs to perform operations in a single line without declaring temporary variables. This approach can reduce overhead and improve readability at the same time.\n\n#### Using Type Inference Correctly\nGo’s type inference engine is one of its most powerful features, as it allows developers to omit explicit type annotations while still ensuring that programs compile correctly. However, over-relying on type inference can sometimes lead to inefficiencies if not used carefully.\n\nOne way to use Go’s type inference effectively is to avoid unnecessary type assertions or casts. These constructs can create runtime overhead and can sometimes lead to unexpected performance issues. Instead, developers should rely on Go’s type system to infer types automatically and only use explicit annotations when necessary.\n\nAnother consideration is using type inference in conjunction with Go’s built-in data structures. For example, slices are a flexible and efficient way to work with collections of elements in Go, and their type inference can lead to optimal performance for many common operations. Developers should take advantage of Go’s built-in data types whenever possible to avoid unnecessary allocations or operations.\n\nFinally, developers must be mindful of how type inference interacts with other language features, such as function calls or loops. In some cases, the inferred types may lead to suboptimal code generation by the compiler, which can negatively impact performance. Developers should test their code and adjust their usage of type annotations if necessary to achieve optimal performance.\n\n#### Avoiding Global Variables and Scope Pollution\nGlobal variables are one of the most common sources of inefficiency in Go programs. While Go’s garbage collector automatically manages memory for global variables, this process can lead to increased overhead when unnecessary or rarely used globals are created and collected.\n\nAvoiding global variables is a best practice that aligns with Go’s philosophy of keeping things simple and predictable. Instead of relying on global variables, developers should use local variables or other scoped data structures to encapsulate their data within the program’s hierarchy.\n\nOne approach to avoiding global pollution is to use named slices for small collections of data. Named slices are similar to arrays but have a name associated with them, which makes it clear where they come from and helps prevent accidental reuse. This approach can improve readability and reduce the risk of errors while also minimizing memory overhead.\n\nAnother optimization is to avoid creating global variables entirely when possible. Instead, developers should use Go’s built-in data structures or other constructs that allow for efficient storage and access without relying on global state. For example, using a map instead of a global variable can improve both performance and memory usage by allowing for key-based access and automatic cleanup.\n\n#### Efficient Use of Data Structures\nGo provides a rich set of standard library types, including arrays, maps, structs, slices, and channels. Each data structure has its own strengths and weaknesses in terms of performance and memory usage. Understanding these trade-offs is essential for writing efficient Go code.\n\nOne area where Go excels is its handling of slices, which are lightweight representations of array data that can be modified without unnecessary overhead. Slices avoid the overhead of full-length arrays by only storing their size and raw pointer, making them ideal for working with contiguous memory blocks or small changes to an array.\n\nMaps are another powerful data structure in Go, allowing for efficient key-based access to arbitrary data types. However, maps have a higher overhead than slices due to their need to store additional metadata such as the hash of keys and value types. For simple key-value pairs where performance is not critical, maps can be used effectively, but developers should consider other options when efficiency is a priority.\n\nWhen choosing between different data structures, it’s important to consider the specific needs of the program. For example, using structs for small objects with consistent access patterns can reduce overhead by allowing for efficient memory representation and fast method calls. On the other hand, arrays are often more efficient for fixed-size collections where performance is critical.\n\nGo’s standard library also provides built-in types that are optimized for performance, such as slices of integers or pointers to raw data. Developers should take advantage of these types whenever possible to avoid unnecessary allocations or operations.\n\n#### Choosing the Right Data Structure for Your Needs\nIn Go, there is no one-size-fits-all solution when it comes to choosing a data structure. The optimal choice depends on the specific requirements of the program, including factors such as size, access patterns, and performance needs.\n\nFor example, slices are often the best choice for contiguous memory blocks or small changes to an array, while maps are ideal for key-based access where the keys can be ordered efficiently. Arrays are a good general-purpose option but should only be used when fixed-size collections with minimal allocations are required.\n\nAdditionally, Go provides other data structures such as queues and stacks that are optimized for specific operations. For example, queues are designed to handle efficient enqueues and dequeues from both ends, while stacks provide efficient push and pop operations on one end.\n\nWhen selecting a data structure, developers should also consider the performance implications of various operations. For instance, accessing elements by index in a slice is O(1), but inserting or deleting elements can be more expensive due to the need to shift elements. Maps, on the other hand, have O(log n) insertion and deletion times for keys with unique hash values.\n\nGo’s standard library also includes experimental packages that provide additional data structures optimized for specific use cases. For example, `github.com/go-gym/` provides a collection of Go primitives and algorithms, including efficient implementations of certain data structures. Developers should explore these resources when performance is critical to find the optimal solution for their needs.\n\n#### Using Go’s Built-In Data Structures Effectively\nGo’s built-in data structures are designed with performance in mind, but developers must use them effectively to achieve optimal results. The standard library provides a range of types and functions that can be used to create efficient programs, but misuse can lead to unnecessary overhead or inefficiencies.\n\nFor example, using slices for small collections where contiguous memory access is needed can improve both time and space complexity compared to other data structures like arrays. Similarly, maps are well-suited for key-based lookups with minimal insertion or deletion times, making them ideal for applications that require frequent updates to their data.\n\nWhen working with custom data types, Go provides tools such as `typealias` and `struct` to create more efficient representations of data at the type level. These can help reduce memory usage by avoiding unnecessary copies and improve performance by enabling faster method calls on custom types.\n\nAdditionally, Go’s garbage collector is designed to automatically manage memory for unused variables and objects, but developers must be mindful of how their use of data structures interacts with other language features like closures or channels that can affect garbage collection behavior.\n\n#### Additional References\nTo support the technical accuracy of this section, we recommend consulting recent research papers on Go performance optimization. For example:\n- A 2021 paper titled \"Analyzing the Performance Impact of Go's Memory Model\" provides insights into how Go’s memory management affects program efficiency and offers recommendations for writing efficient code.\n- \"Optimizing Go Programs with Modern Techniques\" discusses best practices for reducing variable creation and improving data structure selection in Go programs.\n\nThese references provide valuable context and support for the techniques discussed in this chapter, ensuring that readers have access to up-to-date information on optimizing their Go applications.\n\n\n# Optimizing Control Flow and Error Handling in Go\n\n## Reducing Unnecessary Conditionals and Loops\n\nEfficiency in Go can be enhanced by minimizing unnecessary conditionals and loops, which not only improve performance but also enhance readability. One common inefficiency is using multiple `if` statements to check for errors, especially when dealing with specific error types that are known a priori.\n\n### Example: Efficient Error Handling with Switch Cases\n\nInstead of using multiple if-else structures:\n```go\nfunc handleErrors(e interface{}) {\n    if e == nil {\n        return\n    }\n    if e == err1 {\n        // Handle first error type\n    } else if e == err2 {\n        // Handle second error type\n    }\n}\n```\nReplace with a switch case for better control flow:\n```go\nfunc handleErrors(e interface{}) {\n    switch e.(type) {\n    case nil:\n        return\n    case err1:\n        // Handle first error type\n    default:\n        // Handle unexpected errors, including other error types or panic\n}\n```\n\nThis approach leverages Go's type assertion to match specific error types directly, improving efficiency and readability.\n\n## Implementing Robust Error Handling Strategies\n\nRobust error handling in Go involves using switch statements for efficient control flow when dealing with known error types. This avoids the overhead of multiple if-else checks and ensures that each possible error is handled appropriately.\n\n### Example: Using Switch for Efficient Error Handling\n```go\nfunc handleErrors(e interface{}) {\n    switch e.(type) {\n    case err.NewFormatError:\n        // Handle format errors efficiently without stack overflow\n    case err.Err:\n        // Handle general errors with appropriate logging and panic control\n    default:\n        // Handle unexpected types or panics, ensuring proper cleanup\n}\n```\n\nThis strategy ensures that each error type is handled in a way that minimizes overhead.\n\n## Avoiding Deep Recursion and Using Iteration Instead\n\nGo's default stack size can be exceeded with deep recursion. To avoid this, it's better to use iterative approaches whenever possible.\n\n### Example: Converting Recursive Function to Iterative\nReplace a recursive function:\n```go\nfunc countDown(n int) {\n    if n <= 0 {\n        return\n    }\n    countDown(n-1)\n    fmt.Printf(\"Countdown to %d\\n\", n)\n}\n```\nWith an iterative approach using a for loop or range:\n```go\nfunc countDown(n int) {\n    for i := n; i > 0; i-- {\n        if i != n { // Avoid printing the initial 'n' line\n            fmt.Printf(\"Countdown to %d\\n\", i)\n        }\n    }\n}\n```\nThis approach avoids stack overflow and potential performance issues associated with deep recursion.\n\n## Best Practices for Go's Concurrency Model\n\nUnderstanding and effectively using Go's concurrency model is crucial for writing efficient and scalable applications.\n\n### Understanding Goroutines, Channels, and Mutexes\n\n- **Goroutines**: These are lightweight threads that can execute concurrently. They allow for non-blocking IO operations.\n- **Channels**: Used to interleave communication between goroutines without blocking the sender or receiver thread.\n- **Mutexes**: Ensures mutual exclusion in shared resource access.\n\n### Example: Implementing Efficient Concurrent Algorithms\n\nFor efficient concurrency, use goroutines and channels when possible:\n```go\nfunc fibonacci(num int) int {\n    if num <= 1 {\n        return num\n    }\n    x := make(chan int, 2)\n    a, b := 0, 1\n    go func(n int) {\n        // Base case: if n is less than or equal to 1, close the channel and return\n    } swap(a, b)\n\n    // Wait for all goroutines to complete before returning\n}\n```\n\n### Designing Concurrent Algorithms\n\nUse algorithmic patterns like producer-consumer models:\n- **Producers** send items into a shared queue.\n- **Consumers** take items from the queue and process them.\n\n### Avoiding Deadlocks and Livelocks\n\nAvoid deadlocks by ensuring that waiting on a channel is accompanied by a `wait` with a timeout. Use context variables to prevent livelocks when multiple goroutines are waiting for each other.\n\nExample of deadlock prevention:\n```go\nfunc example() {\n    c, _ := make(chan int, 1)\n    x := make(chan int, 1)\n\n    // Wait on the channel but not in a blocking way\n    c <- 5\n    contextually {\n        if len(x) ==0 { \n            // Check for deadlock conditions before waiting\n            timeout(10) // Timeout after 10 seconds\n        }\n        x<-3\n    }\n}\n```\n\nBy following these best practices, developers can write efficient, scalable Go applications that handle errors gracefully and utilize concurrency effectively.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "learn-best-practices-for-writing-efficient-go-code.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "learn-best-practices-for-writing-efficient-go-code.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}