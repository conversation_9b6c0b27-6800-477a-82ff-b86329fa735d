{"title": "Master the Art of Profiling and Optimizing Go Applications", "markdown": {"headingText": "Master the Art of Profiling and Optimizing Go Applications", "containsRefs": false, "markdown": "\nGo is a powerful language known for its simplicity, concurrency model, and built-in optimizations. However, like any programming language, it can be challenging to identify performance bottlenecks in Go applications, especially when dealing with concurrent or high-performance workloads. Profiling tools are essential for diagnosing issues and optimizing code, but understanding how to use them effectively is critical.\n\nThis chapter focuses on the key aspects of profiling and optimizing Go applications, starting with an introduction to profiling, followed by specific techniques for measuring performance, and finally best practices for improving application efficiency. The chapter also explores advanced optimization techniques, such as machine learning-based approaches, to help you build high-performance Go systems.\n\n---\n\n## Profiling Go Applications\n\n### Introduction to Profiling\n\nProfiling is the process of analyzing a program's execution to identify bottlenecks, measure performance metrics, and understand how resources (CPU, memory, etc.) are being used. In Go, profiling helps developers optimize their applications by revealing which parts of the code are performing well and where improvements can be made.\n\nProfiling tools in Go provide detailed insights into the execution flow of a program. By identifying slow or resource-intensive sections, profiling allows you to focus your optimization efforts on areas that will yield the most significant performance gains.\n\n### Using pprof for CPU and Memory Profiling\n\nGo provides a built-in profiling tool called `pprof`, which is part of the standard library (go/pprof). The `PPROF` package offers functions to track CPU usage, memory allocation, and garbage collection (GC) operations. This makes it an ideal tool for measuring performance in Go applications.\n\n#### Example: Using pprof to Profile a Simple Application\n\nLet's consider a simple Go application that measures the CPU time taken by each function:\n\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\tstart := time.Now()\n\t\n\t// Function 1\n\tfor i := 0; i < 1000000; i++ {\n\t\t// Perform some operation, e.g., a simple loop\n\t}\n\t\n\t// Function 2\n\ttime.Sleep(time.Second)\n\t\n\t// Function 3\n\tfor j := 0; j < 500000; j++ {\n\t\t// Another loop with fewer iterations\n\t}\n\t\n\tend := time.Now()\n\t\n println(\"Total CPU time: \", end - start)\n}\n```\n\nTo profile this application, run it with the `--pprof` flag:\n\n```bash\ngo run -v --pprof=percent ./main\n```\n\nThe output will show the percentage of CPU usage for each function and the memory allocation during execution.\n\n#### Example Output:\n```\nPPROF (go/pprof) v2.0.0\nUsing 4 threads, 3MB RAM, 256KB stack\n...\n\n[pprof] start: <func=\"main\" at line=1, column=1>\n[pprof] end: <func=\"main\" at line=7, column=1>\n\nFunctions:\n    <func=\"main\"> : 0.4s (38%)\n    <func=\"func 1\"> : 0.2s (19%)\n    <func=\"func 3\"> : 0.1s (9%)\n...\n\nMemory usage: 1.2MB\n```\n\nThis output shows that `func 1` is responsible for the majority of the CPU time, followed by `func 3`. The memory usage is also low.\n\n---\n\n### Visualizing Profile Data with Go's Built-in Tools\n\nWhile pprof provides detailed data about CPU and memory usage, it can be cumbersome to analyze raw numbers. Go offers built-in tools like `go slice` and `g mem` to visualize profile data in a more user-friendly format.\n\n#### Example: Using go slice to Visualize Profile Data\n\nThe `go slice` tool converts pprof output into a readable table that shows the CPU time, memory usage, and GC operations for each function. It also highlights functions with high memory or CPU usage.\n\nRun the following command to generate a slice of your profile data:\n\n```bash\ngo slice <path/to/pprof-output>\n```\n\nThis will create an HTML file (`slice.html`) that you can open in a web browser to view the visualization.\n\n#### Example Output (simplified):\n\n```html\n<!DOCTYPE html>\n<html>\n<head>\n    <title>Profile Data</title>\n</head>\n<body>\n    <h1>CPU Time by Function</h1>\n    <table border=\"1\">\n        <tr><th>Name</th><th>CPU (%)</th><th>Memory (MB)</th></tr>\n        <tr><td>func 1</td><td>38.0</td><td>0.5</td></tr>\n        <tr><td>func 2</td><td>0.0</td><td>0.0</td></tr>\n        <tr><td>func 3</td><td>9.0</td><td>0.4</td></tr>\n    </table>\n    <h1>Memory Usage by Function</h1>\n    <table border=\"1\">\n        <tr><th>Name</th><th>MB</th></tr>\n        <tr><td>func 1</td><td>0.5</td></tr>\n        <tr><td>func 2</td><td>0.0</td></tr>\n        <tr><td>func 3</td><td>0.4</td></tr>\n    </table>\n</body>\n</html>\n```\n\nThis visualization makes it easier to identify performance hotspots without diving into raw numbers.\n\n---\n\n### Best Practices for Writing Profiles\n\nWhen using pprof, there are several best practices to keep in mind to ensure accurate and meaningful profiling data:\n\n1. **Label Your Profiling Runs**: Use different labels (e.g., `--pprof=label1` and `--pprof=label2`) to distinguish between runs with varying workloads or optimizations.\n\n2. **Set the Interval**: The `--interval` flag determines how often pprof collects data. A smaller interval provides more detailed data but increases overhead. A good starting point is 0.1 seconds, which can be adjusted based on the application's needs.\n\n3. **Focus on Hot Paths**: Many applications have conditional logic or default values that are rarely executed during profiling runs. Ensure that your profiling efforts focus on code paths that are active in typical usage scenarios.\n\n4. **Avoid Overhead**: When measuring performance-critical code, ensure that the profiling tools themselves do not introduce significant overhead. For example, use `--exclude=go` to exclude Go language-related functions from pprof output.\n\nBy following these best practices, you can generate accurate and actionable profile data to guide your optimization efforts.\n\n---\n\n## Optimizing Go Applications\n\n### Understanding Go's Garbage Collection\n\nGo's garbage collector (GC) is designed to automatically manage memory, which simplifies development. However, GC can also introduce overhead in certain scenarios. Optimizing the GC involves tuning its behavior to balance collection frequency and memory usage with application performance requirements.\n\n#### Example: Configuring Garbage Collection\n\nYou can configure Go's GC using environment variables:\n\n- `GCasers`: The number of garbage collection passes.\n- `GCBins`: The minimum size (in bytes) for garbage-collected bins.\n- `GCTick`: The interval at which the garbage collector runs.\n\nFor example, to increase GC performance, you might set these values:\n\n```bash\nexport GCasers=2\nexport GCBins=1024*1024\n```\n\n---\n\n### Avoiding Unnecessary Allocation\n\nGo's memory management is efficient due to its ownership model. However, unnecessary allocations can still impact performance. Here are some strategies to minimize allocation overhead:\n\n#### Example: Restructuring Code for Memory Efficiency\n\nConsider the following code snippet that repeatedly allocates new slices:\n\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\tstart := time.Now()\n\t\n\tfor i := 0; i < 1000000; i++ {\n\t\ta := make([]int, i)\n\t}\n\t\n\tend := time.Now()\n\t\n println(\"Time taken: \", end - start)\n}\n```\n\nThis code allocates a growing slice of integers. To optimize memory usage:\n\n```go\npackage main\n\nimport (\n\t\"time\"\n)\n\nfunc main() {\n\tstart := time.Now()\n\t\n\tfor i := 0; i < 1000000; i++ {\n\t\tif len(a) >= i { // Ensure the slice exists before resizing\n\t\t\ta[i] = i\n\t\t}\n\t} else {\n\t\ta = make([]int, i)\n\t}\n\t\n\tend := time.Now()\n\t\n println(\"Time taken: \", end - start)\n}\n```\n\nThis change avoids unnecessary reallocations by checking if `a` exists before resizing.\n\n---\n\n### Using Cgo to Optimize Performance-Critical Code\n\nFor performance-critical code sections in Go, you can use the compiler plugin `cgo` to optimize them further. CGo compiles Go functions into assembly and performs various optimizations, such as loop unrolling, vectorization, and cache-friendly memory access.\n\n#### Example: Using Cgo to Optimize a Loop\n\nConsider the following benchmark function:\n\n```go\nfunc benchmark() {\n\tstart := time.Now()\n\t\n\tfor i := 0; i < 1e6; i++ {\n\t\ta[i] += 1\n\t}\n\t\n\tend := time.Now()\n\t\n println(\"Time taken: \", end - start)\n}\n```\n\nTo optimize the inner loop using CGo:\n\n```go\nfunc benchmark() {\n\tstart := time.Now()\n\t\n\tfor i := 0; i < 1e6; i++ {\n\t\tcgo(func (a []int) {\n\t\t\ta[i] += 1\n\t\t})\n\t}\n\t\n\tend := time.Now()\n\t\n println(\"Time taken: \", end - start)\n}\n```\n\nAfter compiling with `cgof -O`, the CGo-optimized code runs faster, often achieving near-native performance.\n\n---\n\n### Profiling and Optimizing Go's Built-in Functions\n\nGo's standard library includes many functions that are highly optimized. However, you can still profile and optimize these functions to identify bottlenecks or performance improvements.\n\n#### Example: Benchmarking a Built-In Function\n\nYou can create a benchmark for the `Sort` function in Go:\n\n```go\npackage main\n\nimport (\n\t\"bytes\"\n\t\"\"encoding/json\"\n\t\"fmt\"\n\t\"sync\"\n\t\n\t\"time\"\n)\n\nfunc main() {\n\tstart := time.Now()\n\t\n\t// Generate data\n\tdata, err := bytes.NewBuffer(dataBytes)..ReadAll()\n\tif err != nil {\n\t\tfmt.Printf(\"Error reading data: %v\\n\", err)\n\t\treturn\n\t}\n\t\n\t// Create a sync block to prevent multiple sorts from running simultaneously\n\tvar block SyncBlock{Len: len(data), ChunkSize: 1024}\n\n\tfor i := 0; i < 5; i++ {\n\t\ts, _ := &strings.NewReader(data).Sort()\n\t}\n\t\n\tend := time.Now()\n\t\n\tfmt.Printf(\"Time taken: %v\\n\", end - start)\n\tfmt.Printf(\"Result: %s\\n\", s.String())\n}\n```\n\nBy profiling and benchmarking these functions, you can identify areas where further optimization is needed.\n\n---\n\n### Advanced Profiling and Optimization Techniques\n\n#### Using Go's runtime/debug Package for Low-Level Debugging\n\nThe `runtime/debug` package allows developers to insert debug instrumentation at compile time. This can be useful for debugging performance issues caused by incorrect code rather than micro-optimizations.\n\nFor example, you can enable a debug pass that prints out function calls or memory allocations:\n\n```go\npackage main\n\nimport (\n\t\"debug\"\n\t\"fmt\"\n)\n\nfunc main() {\ndebug.On()\n\tfmt.Printf(\"Main function called at %s\\n\", debug.Now())\n.debugOff()\n\n\treturn 0\n}\n```\n\nThis helps identify where performance bottlenecks are caused by incorrect logic rather than micro-optimized code.\n\n---\n\n#### Implementing Your Own Custom Profilers\n\nIn some cases, existing profiling tools may not meet your needs. You can implement a custom profiler tailored to your application's specific requirements.\n\nA custom profiler might focus on measuring CPU usage for specific functions or provide detailed insights into memory allocation patterns that are unique to your workload.\n\n---\n\n#### Optimizing Go Applications with Machine Learning\n\nMachine learning techniques can be applied to optimize Go applications by analyzing performance data and suggesting optimizations. For example, you could use machine learning models to predict optimal GC settings based on application-specific workloads.\n\nThis approach involves collecting performance metrics using profiling tools, training a model on this data, and then applying the optimized parameters in production.\n\n---\n\n### Best Practices for Optimizing Large-Scale Go Systems\n\nWhen optimizing large-scale Go systems, consider the following best practices:\n\n1. **Profile Early, Profile Often**: Continuously profile your application to identify and address performance issues as they arise.\n2. **Use Tools Correctly**: Understand how each profiling or optimization tool works before using it in production.\n3. **Test Impactfully**: Always test any changes you make to ensure that they do not negatively impact the overall performance of your system.\n4. **Leverage Built-in Optimizations**: Use Go's built-in optimizations, such as GC tuning and CGo, to improve performance without extensive manual optimization.\n\nBy following these best practices, you can build high-performance, scalable Go applications that meet the demands of modern computing environments.\n", "srcMarkdownNoYaml": ""}, "formats": {"html": {"identifier": {"display-name": "HTML", "target-format": "html", "base-format": "html"}, "execute": {"fig-width": 7, "fig-height": 5, "fig-format": "retina", "fig-dpi": 96, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": false, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": false, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "html", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": "auto", "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": [], "notebook-links": true}, "pandoc": {"standalone": true, "wrap": "none", "default-image-extension": "png", "to": "html", "output-file": "master-the-art-of-profiling-and-optimizing-go-applications.html"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"lang": "en", "fig-responsive": true, "quarto-version": "1.6.39", "bibliography": ["../../references.bib"], "theme": "cosmo"}, "extensions": {"book": {"multiFile": true}}}, "pdf": {"identifier": {"display-name": "PDF", "target-format": "pdf", "base-format": "pdf"}, "execute": {"fig-width": 5.5, "fig-height": 3.5, "fig-format": "pdf", "fig-dpi": 300, "df-print": "default", "error": false, "eval": true, "cache": null, "freeze": true, "echo": true, "output": true, "warning": true, "include": true, "keep-md": false, "keep-ipynb": false, "ipynb": null, "enabled": null, "daemon": null, "daemon-restart": false, "debug": false, "ipynb-filters": [], "ipynb-shell-interactivity": null, "plotly-connected": true, "engine": "markdown"}, "render": {"keep-tex": true, "keep-typ": false, "keep-source": false, "keep-hidden": false, "prefer-html": false, "output-divs": true, "output-ext": "pdf", "fig-align": "default", "fig-pos": null, "fig-env": null, "code-fold": "none", "code-overflow": "scroll", "code-link": false, "code-line-numbers": false, "code-tools": false, "tbl-colwidths": true, "merge-includes": true, "inline-includes": false, "preserve-yaml": false, "latex-auto-mk": true, "latex-auto-install": true, "latex-clean": true, "latex-min-runs": 1, "latex-max-runs": 10, "latex-makeindex": "makeindex", "latex-makeindex-opts": [], "latex-tlmgr-opts": [], "latex-input-paths": [], "latex-output-dir": null, "link-external-icon": false, "link-external-newwindow": false, "self-contained-math": false, "format-resources": []}, "pandoc": {"pdf-engine": "lualatex", "standalone": true, "variables": {"graphics": true, "tables": true}, "default-image-extension": "pdf", "to": "pdf", "highlight-style": "printing", "toc": true, "toc-depth": 2, "include-in-header": {"text": "\\usepackage{geometry}\n\\usepackage{wrapfig}\n\\usepackage{fvextra}\n\\usepackage{amsmath}\n\\DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\\\\{\\}}\n\\geometry{\n    paperwidth=6in,\n    paperheight=9in,\n    textwidth=4.5in, % Adjust this to your preferred text width\n    textheight=6.5in,  % Adjust this to your preferred text height\n    inner=0.75in,    % Adjust margins as needed\n    outer=0.75in,\n    top=0.75in,\n    bottom=1in\n}\n\\usepackage{makeidx}\n\\usepackage{tabularx}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{array}\n\\graphicspath{{diagrams/}}\n\\makeindex\n"}, "include-after-body": {"text": "\\printindex\n"}, "output-file": "master-the-art-of-profiling-and-optimizing-go-applications.pdf"}, "language": {"toc-title-document": "Table of contents", "toc-title-website": "On this page", "related-formats-title": "Other Formats", "related-notebooks-title": "Notebooks", "source-notebooks-prefix": "Source", "other-links-title": "Other Links", "code-links-title": "Code Links", "launch-dev-container-title": "Launch Dev Container", "launch-binder-title": "Launch Binder", "article-notebook-label": "Article Notebook", "notebook-preview-download": "Download Notebook", "notebook-preview-download-src": "Download Source", "notebook-preview-back": "Back to Article", "manuscript-meca-bundle": "MECA Bundle", "section-title-abstract": "Abstract", "section-title-appendices": "Appendices", "section-title-footnotes": "Footnotes", "section-title-references": "References", "section-title-reuse": "Reuse", "section-title-copyright": "Copyright", "section-title-citation": "Citation", "appendix-attribution-cite-as": "For attribution, please cite this work as:", "appendix-attribution-bibtex": "BibTeX citation:", "appendix-view-license": "View License", "title-block-author-single": "Author", "title-block-author-plural": "Authors", "title-block-affiliation-single": "Affiliation", "title-block-affiliation-plural": "Affiliations", "title-block-published": "Published", "title-block-modified": "Modified", "title-block-keywords": "Keywords", "callout-tip-title": "Tip", "callout-note-title": "Note", "callout-warning-title": "Warning", "callout-important-title": "Important", "callout-caution-title": "Caution", "code-summary": "Code", "code-tools-menu-caption": "Code", "code-tools-show-all-code": "Show All Code", "code-tools-hide-all-code": "Hide All Code", "code-tools-view-source": "View Source", "code-tools-source-code": "Source Code", "tools-share": "Share", "tools-download": "Download", "code-line": "Line", "code-lines": "Lines", "copy-button-tooltip": "Copy to Clipboard", "copy-button-tooltip-success": "Copied!", "repo-action-links-edit": "Edit this page", "repo-action-links-source": "View source", "repo-action-links-issue": "Report an issue", "back-to-top": "Back to top", "search-no-results-text": "No results", "search-matching-documents-text": "matching documents", "search-copy-link-title": "Copy link to search", "search-hide-matches-text": "Hide additional matches", "search-more-match-text": "more match in this document", "search-more-matches-text": "more matches in this document", "search-clear-button-title": "Clear", "search-text-placeholder": "", "search-detached-cancel-button-title": "Cancel", "search-submit-button-title": "Submit", "search-label": "Search", "toggle-section": "Toggle section", "toggle-sidebar": "Toggle sidebar navigation", "toggle-dark-mode": "Toggle dark mode", "toggle-reader-mode": "Toggle reader mode", "toggle-navigation": "Toggle navigation", "crossref-fig-title": "Figure", "crossref-tbl-title": "Table", "crossref-lst-title": "Listing", "crossref-thm-title": "<PERSON><PERSON>", "crossref-lem-title": "Lemma", "crossref-cor-title": "Corollary", "crossref-prp-title": "Proposition", "crossref-cnj-title": "Conjecture", "crossref-def-title": "Definition", "crossref-exm-title": "Example", "crossref-exr-title": "Exercise", "crossref-ch-prefix": "Chapter", "crossref-apx-prefix": "A<PERSON>ndix", "crossref-sec-prefix": "Section", "crossref-eq-prefix": "Equation", "crossref-lof-title": "List of Figures", "crossref-lot-title": "List of Tables", "crossref-lol-title": "List of Listings", "environment-proof-title": "Proof", "environment-remark-title": "Remark", "environment-solution-title": "Solution", "listing-page-order-by": "Order By", "listing-page-order-by-default": "<PERSON><PERSON><PERSON>", "listing-page-order-by-date-asc": "Oldest", "listing-page-order-by-date-desc": "Newest", "listing-page-order-by-number-desc": "High to Low", "listing-page-order-by-number-asc": "Low to High", "listing-page-field-date": "Date", "listing-page-field-title": "Title", "listing-page-field-description": "Description", "listing-page-field-author": "Author", "listing-page-field-filename": "File Name", "listing-page-field-filemodified": "Modified", "listing-page-field-subtitle": "Subtitle", "listing-page-field-readingtime": "Reading Time", "listing-page-field-wordcount": "Word Count", "listing-page-field-categories": "Categories", "listing-page-minutes-compact": "{0} min", "listing-page-category-all": "All", "listing-page-no-matches": "No matching items", "listing-page-words": "{0} words", "listing-page-filter": "Filter", "draft": "Draft"}, "metadata": {"block-headings": true, "bibliography": ["../../references.bib"], "documentclass": "scrbook", "lof": false, "lot": false, "float": true, "classoption": "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt", "fig-cap-location": "top", "urlcolor": "blue", "linkcolor": "black", "biblio-style": "apalike", "code-block-bg": "#f0f0f0", "code-block-border-left": "#000000", "mermaid": {"theme": "neutral"}, "fontfamily": "liber<PERSON>us", "monofont": "Consolas", "monofontoptions": ["Scale=0.7"], "indent": true}, "extensions": {"book": {"selfContainedOutput": true}}}}, "projectFormats": ["html", "pdf"]}