project:
  type: book

book:
  title: "The Complete Guide to GoLang"
  author: "Generative Ai"
  date: "11/2/2025"
  chapters:
    - index.qmd
    - part: parts/go-fundamentals/intro.qmd
      chapters:
        - parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.qmd
        - parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.qmd
        - parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.qmd
    - part: parts/concurrent-programming/intro.qmd
      chapters:
        - parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.qmd
        - parts/concurrent-programming/master-the-art-of-parallelism-in-go.qmd
    - part: parts/complex-data-structures/intro.qmd
      chapters:
        - parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.qmd
        - parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.qmd
    - part: parts/real-world-applications/intro.qmd
      chapters:
        - parts/real-world-applications/build-a-scalable-web-service-using-go.qmd
        - parts/real-world-applications/implement-a-distributed-system-with-go.qmd
    - part: parts/optimization-techniques/intro.qmd
      chapters:
        - parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.qmd
        - parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.qmd
    - part: parts/error-handling-and-testing/intro.qmd
      chapters:
        - parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.qmd
        - parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.qmd
    - part: parts/advanced-topics/intro.qmd
      chapters:
        - parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.qmd
        - parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.qmd
    - part: parts/case-studies-and-best-practices/intro.qmd
      chapters:
        - parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.qmd
        - parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.qmd
    - part: parts/future-proofing-your-go-code/intro.qmd
      chapters:
        - parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.qmd
        - parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.qmd
    - summary.qmd

bibliography: references.bib

format:
  html:
    theme: cosmo
  pdf:
    documentclass: scrbook
    highlight-style: printing
    pdf-engine: lualatex
    freeze: true
    lof: false
    lot: false
    toc: true
    float: true
    toc-depth: 2
    classoption: "paper=6in:9in,pagesize=pdftex,footinclude=on,11pt"
    fig-cap-location: top
    urlcolor: "blue"
    linkcolor: "black"
    biblio-style: apalike
    keep-tex: true
    code-block-bg: "#f0f0f0"
    code-block-border-left: "#000000"
    mermaid:
      theme: neutral
    include-in-header:
      text: |
        \usepackage{geometry}
        \usepackage{wrapfig}
        \usepackage{fvextra}
        \usepackage{amsmath}
        \DefineVerbatimEnvironment{Highlighting}{Verbatim}{breaklines,commandchars=\\\{\}}
        \geometry{
            paperwidth=6in,
            paperheight=9in,
            textwidth=4.5in, % Adjust this to your preferred text width
            textheight=6.5in,  % Adjust this to your preferred text height
            inner=0.75in,    % Adjust margins as needed
            outer=0.75in,
            top=0.75in,
            bottom=1in
        }
        \usepackage{makeidx}
        \usepackage{tabularx}
        \usepackage{float}
        \usepackage{graphicx}
        \usepackage{array}
        \graphicspath{{diagrams/}}
        \makeindex
    include-after-body:
      text: |
        \printindex
    fontfamily: libertinus
    monofont: Consolas
    monofontoptions:
      - Scale=0.7
    indent: true



