
# 2025-05-25 13:18:21.910110
+yes

# 2025-05-25 13:18:40.405587
+fix the headers hierarchy in all the .qmd files in this project

# 2025-05-25 13:19:30.211562
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/**.qmd

# 2025-05-25 13:20:38.196887
+/home/<USER>/development/books/the-complete-guide-to-golang/index.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/advanced-topics/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/case-studies-and-best-practices/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/complex-data-structures/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/concurrent-programming/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/concurrent-programming/master-the-art-of-parallelism-in-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/error-handling-and-testing/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/future-proofing-your-go-code/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/go-fundamentals/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/optimization-techniques/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/real-world-applications/build-a-scalable-web-service-using-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/real-world-applications/implement-a-distributed-system-with-go.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/parts/real-world-applications/intro.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/references.qmd
+/home/<USER>/development/books/the-complete-guide-to-golang/summary.qmd

# 2025-05-25 13:20:58.788607
+fix the headers heirarchy in these files in the project \

# 2025-05-25 13:21:04.115385
+\

# 2025-05-25 13:21:08.461584
+y

# 2025-05-25 13:21:08.468098
+/run find . -name "*.qmd" | xargs cat

# 2025-05-25 13:21:08.468220
+/run find . -name "*.qmd" | xargs cat

# 2025-05-25 13:21:13.079550
+y

# 2025-05-25 13:21:31.102845
+/exit
