<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>6&nbsp; 5. Trees – The Complete Guide to GoLang</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="../../site_libs/quarto-nav/quarto-nav.js"></script>
<script src="../../site_libs/quarto-nav/headroom.min.js"></script>
<script src="../../site_libs/clipboard/clipboard.min.js"></script>
<script src="../../site_libs/quarto-search/autocomplete.umd.js"></script>
<script src="../../site_libs/quarto-search/fuse.min.js"></script>
<script src="../../site_libs/quarto-search/quarto-search.js"></script>
<meta name="quarto:offset" content="../../">
<link href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" rel="next">
<link href="../../parts/complex-data-structures/intro.html" rel="prev">
<script src="../../site_libs/quarto-html/quarto.js"></script>
<script src="../../site_libs/quarto-html/popper.min.js"></script>
<script src="../../site_libs/quarto-html/tippy.umd.min.js"></script>
<script src="../../site_libs/quarto-html/anchor.min.js"></script>
<link href="../../site_libs/quarto-html/tippy.css" rel="stylesheet">
<link href="../../site_libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="../../site_libs/bootstrap/bootstrap.min.js"></script>
<link href="../../site_libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="../../site_libs/bootstrap/bootstrap-a2a08d6480f1a07d2e84f5b3bded3372.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<script id="quarto-search-options" type="application/json">{
  "location": "sidebar",
  "copy-button": false,
  "collapse-after": 3,
  "panel-placement": "start",
  "type": "textbox",
  "limit": 50,
  "keyboard-shortcut": [
    "f",
    "/",
    "s"
  ],
  "show-item-context": false,
  "language": {
    "search-no-results-text": "No results",
    "search-matching-documents-text": "matching documents",
    "search-copy-link-title": "Copy link to search",
    "search-hide-matches-text": "Hide additional matches",
    "search-more-match-text": "more match in this document",
    "search-more-matches-text": "more matches in this document",
    "search-clear-button-title": "Clear",
    "search-text-placeholder": "",
    "search-detached-cancel-button-title": "Cancel",
    "search-submit-button-title": "Submit",
    "search-label": "Search"
  }
}</script>


</head>

<body class="nav-sidebar floating">

<div id="quarto-search-results"></div>
  <header id="quarto-header" class="headroom fixed-top">
  <nav class="quarto-secondary-nav">
    <div class="container-fluid d-flex">
      <button type="button" class="quarto-btn-toggle btn" data-bs-toggle="collapse" role="button" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">
        <i class="bi bi-layout-text-sidebar-reverse"></i>
      </button>
        <nav class="quarto-page-breadcrumbs" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/complex-data-structures/intro.html">Complex Data Structures</a></li><li class="breadcrumb-item"><a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></a></li></ol></nav>
        <a class="flex-grow-1" role="navigation" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
        </a>
      <button type="button" class="btn quarto-search-button" aria-label="Search" onclick="window.quartoOpenSearch();">
        <i class="bi bi-search"></i>
      </button>
    </div>
  </nav>
</header>
<!-- content -->
<div id="quarto-content" class="quarto-container page-columns page-rows-contents page-layout-article">
<!-- sidebar -->
  <nav id="quarto-sidebar" class="sidebar collapse collapse-horizontal quarto-sidebar-collapse-item sidebar-navigation floating overflow-auto">
    <div class="pt-lg-2 mt-2 text-left sidebar-header">
    <div class="sidebar-title mb-0 py-0">
      <a href="../../">The Complete Guide to GoLang</a> 
    </div>
      </div>
        <div class="mt-2 flex-shrink-0 align-items-center">
        <div class="sidebar-search">
        <div id="quarto-search" class="" title="Search"></div>
        </div>
        </div>
    <div class="sidebar-menu-container"> 
    <ul class="list-unstyled mt-1">
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../index.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Preface</span></a>
  </div>
</li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/go-fundamentals/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Go Fundamentals</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-1" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-1" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">1</span>&nbsp; <span class="chapter-title">Introduction to Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">2</span>&nbsp; <span class="chapter-title">Arrays and Slices</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">3</span>&nbsp; <span class="chapter-title">Explore Interfaces, Error Handling, and Package Management</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/concurrent-programming/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Concurrent Programming</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-2" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-2" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">4</span>&nbsp; <span class="chapter-title">Unravel the Power of Go’s Concurrency Model with Goroutines and Channels</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/complex-data-structures/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Complex Data Structures</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-3" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-3" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html" class="sidebar-item-text sidebar-link active">
 <span class="menu-text"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">7</span>&nbsp; <span class="chapter-title">Mastering Sorting and Searching Complex Data in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/real-world-applications/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Real-World Applications</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-4" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-4" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">8</span>&nbsp; <span class="chapter-title">Build a Scalable Web Service Using Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/optimization-techniques/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Optimization Techniques</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-5" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-5" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">10</span>&nbsp; <span class="chapter-title">Writing Efficient Go Code</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">11</span>&nbsp; <span class="chapter-title">Master the Art of Profiling and Optimizing Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/error-handling-and-testing/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Error Handling and Testing</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-6" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-6" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">12</span>&nbsp; <span class="chapter-title">Overview of Error Handling in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/advanced-topics/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Advanced Topics</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-7" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-7" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">14</span>&nbsp; <span class="chapter-title">What are Coroutines?</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">15</span>&nbsp; <span class="chapter-title">Introduction to Concurrent Programming</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/case-studies-and-best-practices/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Case Studies and Best Practices</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-8" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-8" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">16</span>&nbsp; <span class="chapter-title">Case Study: Building a Scalable Backend with Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">17</span>&nbsp; <span class="chapter-title">Introduction to Writing Maintainable and Scalable Code in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/future-proofing-your-go-code/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Future-Proofing Your Go Code</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-9" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-9" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">18</span>&nbsp; <span class="chapter-title">Learn How to Write Future-Proof Code in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">19</span>&nbsp; <span class="chapter-title">Mastering Adaptability</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../summary.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">20</span>&nbsp; <span class="chapter-title">Summary</span></span></a>
  </div>
</li>
    </ul>
    </div>
</nav>
<div id="quarto-sidebar-glass" class="quarto-sidebar-collapse-item" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item"></div>
<!-- margin-sidebar -->
    <div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
        <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Table of contents</h2>
   
  <ul>
  <li><a href="#graphs" id="toc-graphs" class="nav-link active" data-scroll-target="#graphs"><span class="header-section-number">6.0.1</span> 5. Graphs</a></li>
  <li><a href="#conclusion" id="toc-conclusion" class="nav-link" data-scroll-target="#conclusion"><span class="header-section-number">6.0.2</span> Conclusion</a></li>
  <li><a href="#advanced-topics-in-trees-and-graphs" id="toc-advanced-topics-in-trees-and-graphs" class="nav-link" data-scroll-target="#advanced-topics-in-trees-and-graphs"><span class="header-section-number">7</span> Advanced Topics in Trees and Graphs</a>
  <ul class="collapse">
  <li><a href="#tree-traversal" id="toc-tree-traversal" class="nav-link" data-scroll-target="#tree-traversal"><span class="header-section-number">7.1</span> Tree Traversal</a>
  <ul class="collapse">
  <li><a href="#in-order-traversal" id="toc-in-order-traversal" class="nav-link" data-scroll-target="#in-order-traversal"><span class="header-section-number">7.1.1</span> In-order Traversal</a></li>
  <li><a href="#pre-order-traversal" id="toc-pre-order-traversal" class="nav-link" data-scroll-target="#pre-order-traversal"><span class="header-section-number">7.1.2</span> Pre-order Traversal</a></li>
  <li><a href="#post-order-traversal" id="toc-post-order-traversal" class="nav-link" data-scroll-target="#post-order-traversal"><span class="header-section-number">7.1.3</span> Post-order Traversal</a></li>
  </ul></li>
  <li><a href="#graph-search-algorithms" id="toc-graph-search-algorithms" class="nav-link" data-scroll-target="#graph-search-algorithms"><span class="header-section-number">7.2</span> Graph Search Algorithms</a>
  <ul class="collapse">
  <li><a href="#breadth-first-search-bfs" id="toc-breadth-first-search-bfs" class="nav-link" data-scroll-target="#breadth-first-search-bfs"><span class="header-section-number">7.2.1</span> Breadth-First Search (BFS)</a></li>
  <li><a href="#depth-first-search-dfs" id="toc-depth-first-search-dfs" class="nav-link" data-scroll-target="#depth-first-search-dfs"><span class="header-section-number">7.2.2</span> Depth-First Search (DFS)</a></li>
  <li><a href="#applications-of-graph-search-algorithms" id="toc-applications-of-graph-search-algorithms" class="nav-link" data-scroll-target="#applications-of-graph-search-algorithms"><span class="header-section-number">7.2.3</span> Applications of Graph Search Algorithms</a></li>
  </ul></li>
  <li><a href="#minimum-spanning-tree-mst" id="toc-minimum-spanning-tree-mst" class="nav-link" data-scroll-target="#minimum-spanning-tree-mst"><span class="header-section-number">7.3</span> Minimum Spanning Tree (MST)</a>
  <ul class="collapse">
  <li><a href="#kruskals-algorithm" id="toc-kruskals-algorithm" class="nav-link" data-scroll-target="#kruskals-algorithm"><span class="header-section-number">7.3.1</span> Kruskal’s Algorithm</a></li>
  <li><a href="#prims-algorithm" id="toc-prims-algorithm" class="nav-link" data-scroll-target="#prims-algorithm"><span class="header-section-number">7.3.2</span> Prim’s Algorithm</a></li>
  <li><a href="#applications-of-mst" id="toc-applications-of-mst" class="nav-link" data-scroll-target="#applications-of-mst"><span class="header-section-number">7.3.3</span> Applications of MST</a></li>
  </ul></li>
  </ul></li>
  </ul>
</nav>
    </div>
<!-- main -->
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default"><nav class="quarto-page-breadcrumbs quarto-title-breadcrumbs d-none d-lg-block" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/complex-data-structures/intro.html">Complex Data Structures</a></li><li class="breadcrumb-item"><a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></a></li></ol></nav>
<div class="quarto-title">
<h1 class="title"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<section id="what-are-trees" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="what-are-trees"><span class="header-section-number">*******</span> 5.1 What Are Trees?</h4>
<p>A tree is a non-linear data structure consisting of nodes connected hierarchically. Each node can have multiple child nodes, but only one parent (except for the root node). This hierarchical structure allows efficient searching and traversal operations.</p>
<p>For example:</p>
<pre><code>        A
       / \
      B   C
     / \
    D   E</code></pre>
<p>In Go code:</p>
<div class="sourceCode" id="cb2"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="kw">type</span> Tree <span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>    Root <span class="op">*</span>Node</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="why-use-trees-in-programming" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="why-use-trees-in-programming"><span class="header-section-number">*******</span> 5.2 Why Use Trees in Programming?</h4>
<p>Trees are essential for organizing data hierarchically, enabling efficient searching and insertion operations with logarithmic time complexity (O(log n)). They are widely used in:</p>
<ul>
<li><strong>File systems</strong>: Representing directory structures.</li>
<li><strong>Databases</strong>: Implementing B-trees for disk-based storage efficiency.</li>
<li><strong>Algorithms</strong>: Huffman coding, decision trees.</li>
</ul>
</section>
<section id="basic-tree-operations" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="basic-tree-operations"><span class="header-section-number">*******</span> 5.3 Basic Tree Operations</h4>
<ol type="1">
<li><p><strong>Traversal Methods</strong>:</p>
<ul>
<li>Pre-order: Visit node, then left child, then right child.</li>
<li>In-order: Left child, visit node, then right child.</li>
<li>Post-order: Left child, right child, visit node.</li>
</ul></li>
<li><p><strong>Finding Tree Height</strong>: The longest path from root to leaf.</p></li>
<li><p><strong>Balanced Trees</strong>: Ensure height difference between subtrees is ≤1 for optimal performance.</p></li>
<li><p><strong>Searching Elements</strong>: Efficient in balanced trees (O(log n)) but slower in skewed trees.</p></li>
<li><p><strong>Inserting Nodes</strong>: Add nodes based on hierarchical structure.</p></li>
<li><p><strong>Deleting Nodes</strong>: Remove from appropriate subtree or replace with child node if necessary.</p></li>
<li><p><strong>Iterators</strong>: Allow traversal without recursion, using explicit stack structures.</p></li>
</ol>
<p>Example code for a tree traversal:</p>
<div class="sourceCode" id="cb3"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> preOrder<span class="op">(</span>node <span class="op">*</span>Node<span class="op">)</span> <span class="op">{</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> node <span class="op">==</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>    fmt<span class="op">.</span>Printf<span class="op">(</span><span class="st">"Visited %s</span><span class="ch">\n</span><span class="st">"</span><span class="op">,</span> node<span class="op">.</span>Value<span class="op">)</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    preOrder<span class="op">(</span>node<span class="op">.</span>Left<span class="op">)</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>    preOrder<span class="op">(</span>node<span class="op">.</span>Right<span class="op">)</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="tree-implementations" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="tree-implementations"><span class="header-section-number">*******</span> 5.4 Tree Implementations</h4>
<section id="binary-search-tree-bst" class="level5" data-number="*******.1">
<h5 data-number="*******.1" class="anchored" data-anchor-id="binary-search-tree-bst"><span class="header-section-number">*******.1</span> Binary Search Tree (BST)</h5>
<p>A BST is a tree where each left subtree has nodes with smaller values, and right subtrees have larger ones.</p>
<ul>
<li><strong>Insertion</strong>: Compare node value with current node to decide direction.</li>
</ul>
<div class="sourceCode" id="cb4"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> insert<span class="op">(</span>root <span class="op">*</span>Node<span class="op">,</span> value <span class="dt">int</span><span class="op">)</span> <span class="op">*</span>Node <span class="op">{</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> root <span class="op">==</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="op">&amp;</span>Node<span class="op">{</span>Value<span class="op">:</span> value<span class="op">}</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> value <span class="op">&lt;</span> root<span class="op">.</span>Value <span class="op">{</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>        root<span class="op">.</span>Left <span class="op">=</span> insert<span class="op">(</span>root<span class="op">.</span>Left<span class="op">,</span> value<span class="op">)</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span> <span class="cf">else</span> <span class="op">{</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>        root<span class="op">.</span>Right <span class="op">=</span> insert<span class="op">(</span>root<span class="op">.</span>Right<span class="op">,</span> value<span class="op">)</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> root</span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<ul>
<li><strong>Deletion</strong>: Remove node based on subtree size or replace with child.</li>
</ul>
</section>
<section id="b-tree" class="level5" data-number="*******.2">
<h5 data-number="*******.2" class="anchored" data-anchor-id="b-tree"><span class="header-section-number">*******.2</span> B-Tree</h5>
<p>A B-tree is a balanced tree structure used in databases for efficient disk operations. Each internal node can have up to ‘n’ keys and children, reducing I/O operations.</p>
<p>Example code snippet:</p>
<div class="sourceCode" id="cb5"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="kw">type</span> BTreeNode <span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>    Keys      <span class="op">[]</span><span class="dt">int</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>    Children   <span class="op">[][]</span>BTreeNode</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a>    Leaf       <span class="dt">bool</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>b <span class="op">*</span>BTreeNode<span class="op">)</span> AddKey<span class="op">(</span>key <span class="dt">int</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Implementation details for insertion logic</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="heap-based-trees" class="level5" data-number="*******.3">
<h5 data-number="*******.3" class="anchored" data-anchor-id="heap-based-trees"><span class="header-section-number">*******.3</span> Heap-Based Trees</h5>
<p>A heap is a complete binary tree where parent nodes are ordered with respect to their children. It supports efficient extraction of max or min elements.</p>
<p>Example code:</p>
<div class="sourceCode" id="cb6"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="kw">type</span> MinHeap <span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>    heap <span class="op">[]</span><span class="dt">int</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>h <span class="op">*</span>MinHeap<span class="op">)</span> Push<span class="op">(</span>x <span class="dt">int</span><span class="op">)</span> <span class="op">{</span> h<span class="op">.</span>heap <span class="op">=</span> <span class="bu">append</span><span class="op">(</span>h<span class="op">.</span>heap<span class="op">,</span> x<span class="op">)</span> <span class="op">}</span></span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>h <span class="op">*</span>MinHeap<span class="op">)</span> Pop<span class="op">()</span> <span class="dt">int</span> <span class="op">{</span></span>
<span id="cb6-7"><a href="#cb6-7" aria-hidden="true" tabindex="-1"></a>    <span class="bu">len</span> <span class="op">:=</span> <span class="bu">len</span><span class="op">(</span>h<span class="op">.</span>heap<span class="op">)</span></span>
<span id="cb6-8"><a href="#cb6-8" aria-hidden="true" tabindex="-1"></a>    val <span class="op">:=</span> h<span class="op">.</span>heap<span class="op">[</span><span class="dv">0</span><span class="op">]</span></span>
<span id="cb6-9"><a href="#cb6-9" aria-hidden="true" tabindex="-1"></a>    h<span class="op">.</span>heap <span class="op">=</span> h<span class="op">.</span>heap<span class="op">[</span><span class="dv">1</span><span class="op">:</span><span class="bu">len</span><span class="op">]</span></span>
<span id="cb6-10"><a href="#cb6-10" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> val</span>
<span id="cb6-11"><a href="#cb6-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="recent-research" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="recent-research"><span class="header-section-number">*******</span> Recent Research</h4>
<p>Recent advancements in tree structures include the development of <strong>Red-Black Trees</strong> for efficient rebalancing and <strong>AVL Trees</strong> for stricter balance, enhancing performance across various applications.</p>
<hr>
</section>
<section id="graphs" class="level3" data-number="6.0.1">
<h3 data-number="6.0.1" class="anchored" data-anchor-id="graphs"><span class="header-section-number">6.0.1</span> 5. Graphs</h3>
<section id="what-are-graphs" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="what-are-graphs"><span class="header-section-number">*******</span> What Are Graphs?</h4>
<p>A graph consists of vertices (nodes) connected by edges, representing complex relationships like social networks or road maps.</p>
<p>For example:</p>
<pre><code>A -- B -- C
|    |    
D -- E -- F</code></pre>
<p>In Go code:</p>
<div class="sourceCode" id="cb8"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="kw">type</span> Edge <span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a>    To   <span class="dt">int</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a><span class="kw">type</span> Graph <span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    Vertices <span class="kw">map</span><span class="op">[</span><span class="dt">int</span><span class="op">]</span>Node</span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>    Edges   <span class="op">[]</span>Edge</span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="types-of-graphs" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="types-of-graphs"><span class="header-section-number">*******</span> Types of Graphs</h4>
<ul>
<li><strong>Directed vs.&nbsp;Undirected</strong>: Edges have direction in directed graphs, but are bidirectional in undirected ones.</li>
<li><strong>Weighted vs.&nbsp;Unweighted</strong>: Edges may carry weights or values.</li>
<li><strong>Cyclic vs.&nbsp;Acyclic</strong>: Cycles exist when a node can reach itself; acyclic graphs avoid this.</li>
<li><strong>Sparse vs.&nbsp;Dense</strong>: Based on the number of edges relative to possible connections.</li>
</ul>
<p>Example: Social media platforms use undirected, unweighted graphs without cycles (except for mutual connections).</p>
</section>
<section id="basic-graph-operations" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="basic-graph-operations"><span class="header-section-number">*******</span> Basic Graph Operations</h4>
<ol type="1">
<li><strong>Adding/Removing Vertices/Edges</strong>: Update graph structure accordingly.</li>
<li><strong>Checking Adjacency</strong>: Determine if an edge exists between two vertices.</li>
<li><strong>Traversing Graphs</strong>: Use Depth-First Search (DFS) or Breadth-First Search (BFS).</li>
<li><strong>Shortest Path Finding</strong>: Implement Dijkstra’s algorithm for weighted graphs.</li>
<li><strong>Cycle Detection</strong>: Track visited nodes to prevent revisiting and detect cycles.</li>
<li><strong>Calculating Properties</strong>: Determine vertex degrees, connected components, etc.</li>
</ol>
<p>Example code for graph traversal:</p>
<div class="sourceCode" id="cb9"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> BFS<span class="op">(</span>graph <span class="op">*</span>Graph<span class="op">,</span> start <span class="dt">int</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>    queue <span class="op">:=</span> <span class="bu">make</span><span class="op">([]</span><span class="dt">int</span><span class="op">,</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>    queue <span class="op">=</span> <span class="bu">append</span><span class="op">(</span>queue<span class="op">,</span> start<span class="op">)</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    visited <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">map</span><span class="op">[</span><span class="dt">int</span><span class="op">]</span><span class="dt">bool</span><span class="op">)</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> <span class="bu">len</span><span class="op">(</span>queue<span class="op">)</span> <span class="op">&gt;</span> <span class="dv">0</span> <span class="op">{</span></span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>        current <span class="op">:=</span> queue<span class="op">[</span><span class="dv">0</span><span class="op">]</span></span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>        visited<span class="op">[</span>current<span class="op">]</span> <span class="op">=</span> <span class="ot">true</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> graph<span class="op">.</span>Edges contains edge to other nodes<span class="op">:</span></span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>            add them to the queue</span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="recent-research-1" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="recent-research-1"><span class="header-section-number">*******</span> Recent Research</h4>
<p>Research in graph algorithms has led to advancements like <strong>Union-Find</strong> data structures for efficient connectivity management and <strong>Topological Sorting</strong> for dependency resolution.</p>
<hr>
</section>
</section>
<section id="conclusion" class="level3" data-number="6.0.2">
<h3 data-number="6.0.2" class="anchored" data-anchor-id="conclusion"><span class="header-section-number">6.0.2</span> Conclusion</h3>
<p>Trees and graphs are fundamental data structures offering unique capabilities for organizing, searching, and traversing complex data relationships. Mastery of these concepts is crucial for developing efficient and scalable applications in Go and beyond.</p>
</section>
<section id="advanced-topics-in-trees-and-graphs" class="level1" data-number="7">
<h1 data-number="7"><span class="header-section-number">7</span> Advanced Topics in Trees and Graphs</h1>
<section id="tree-traversal" class="level2" data-number="7.1">
<h2 data-number="7.1" class="anchored" data-anchor-id="tree-traversal"><span class="header-section-number">7.1</span> Tree Traversal</h2>
<p>Tree traversal refers to the process of visiting each node in a tree exactly once in a specific order. Common tree traversal algorithms include In-order, Pre-order, and Post-order traversals.</p>
<section id="in-order-traversal" class="level3" data-number="7.1.1">
<h3 data-number="7.1.1" class="anchored" data-anchor-id="in-order-traversal"><span class="header-section-number">7.1.1</span> In-order Traversal</h3>
<p>In-order traversal visits nodes by first traversing the left subtree, then visiting the root node, and finally traversing the right subtree. This method is often used to retrieve data in sorted order for binary search trees.</p>
<section id="example-code" class="level4" data-number="7.1.1.1">
<h4 data-number="7.1.1.1" class="anchored" data-anchor-id="example-code"><span class="header-section-number">7.1.1.1</span> Example Code:</h4>
<div class="sourceCode" id="cb10"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> InOrderTraverse<span class="op">(</span>node <span class="op">*</span>TreeNode<span class="op">)</span> <span class="op">{</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> node <span class="op">==</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    InOrderTraverse<span class="op">(</span>node<span class="op">.</span>Left<span class="op">)</span></span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    fmt<span class="op">.</span>Printf<span class="op">(</span><span class="st">"%v "</span><span class="op">,</span> node<span class="op">.</span>Value<span class="op">)</span></span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    InOrderTraverse<span class="op">(</span>node<span class="op">.</span>Right<span class="op">)</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="pre-order-traversal" class="level3" data-number="7.1.2">
<h3 data-number="7.1.2" class="anchored" data-anchor-id="pre-order-traversal"><span class="header-section-number">7.1.2</span> Pre-order Traversal</h3>
<p>Pre-order traversal visits the root node first, then recursively traverses the left and right subtrees. This method is useful for creating copies of trees or when a node needs to be processed before its children.</p>
<section id="example-code-1" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="example-code-1"><span class="header-section-number">*******</span> Example Code:</h4>
<div class="sourceCode" id="cb11"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> PreOrderTraverse<span class="op">(</span>node <span class="op">*</span>TreeNode<span class="op">)</span> <span class="op">{</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> node <span class="op">==</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>    fmt<span class="op">.</span>Printf<span class="op">(</span><span class="st">"%v "</span><span class="op">,</span> node<span class="op">.</span>Value<span class="op">)</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    PreOrderTraverse<span class="op">(</span>node<span class="op">.</span>Left<span class="op">)</span></span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    PreOrderTraverse<span class="op">(</span>node<span class="op">.</span>Right<span class="op">)</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="post-order-traversal" class="level3" data-number="7.1.3">
<h3 data-number="7.1.3" class="anchored" data-anchor-id="post-order-traversal"><span class="header-section-number">7.1.3</span> Post-order Traversal</h3>
<p>Post-order traversal visits the left subtree, then the right subtree, and finally the root node. This method is useful for deleting trees or when a node’s processing depends on its children.</p>
<section id="example-code-2" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="example-code-2"><span class="header-section-number">*******</span> Example Code:</h4>
<div class="sourceCode" id="cb12"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> PostOrderTraverse<span class="op">(</span>node <span class="op">*</span>TreeNode<span class="op">)</span> <span class="op">{</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> node <span class="op">==</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a>    PostOrderTraverse<span class="op">(</span>node<span class="op">.</span>Left<span class="op">)</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    PostOrderTraverse<span class="op">(</span>node<span class="op">.</span>Right<span class="op">)</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>    fmt<span class="op">.</span>Printf<span class="op">(</span><span class="st">"%v "</span><span class="op">,</span> node<span class="op">.</span>Value<span class="op">)</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>These traversal methods are fundamental in various applications, such as parsing expressions or searching for specific data within a tree structure.</p>
</section>
</section>
</section>
<section id="graph-search-algorithms" class="level2" data-number="7.2">
<h2 data-number="7.2" class="anchored" data-anchor-id="graph-search-algorithms"><span class="header-section-number">7.2</span> Graph Search Algorithms</h2>
<p>Graph search algorithms are used to traverse or search through graph structures. Two of the most common algorithms are Breadth-First Search (BFS) and Depth-First Search (DFS).</p>
<section id="breadth-first-search-bfs" class="level3" data-number="7.2.1">
<h3 data-number="7.2.1" class="anchored" data-anchor-id="breadth-first-search-bfs"><span class="header-section-number">7.2.1</span> Breadth-First Search (BFS)</h3>
<p>BFS explores all nodes at the present depth before moving on to nodes at the next depth level. It uses a queue data structure and is useful for finding the shortest path in unweighted graphs or determining the connected components of a graph.</p>
<section id="example-code-3" class="level4" data-number="7.2.1.1">
<h4 data-number="7.2.1.1" class="anchored" data-anchor-id="example-code-3"><span class="header-section-number">7.2.1.1</span> Example Code:</h4>
<div class="sourceCode" id="cb13"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> BFS<span class="op">(</span>graph <span class="kw">map</span><span class="op">[</span>Vertex<span class="op">]</span>Edges<span class="op">,</span> startVertex Vertex<span class="op">)</span> <span class="op">{</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a>    visited <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">map</span><span class="op">&lt;</span>Vertex<span class="op">]</span><span class="dt">bool</span><span class="op">)</span></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a>    queue <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span>Queue<span class="op">)</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a>    enqueue<span class="op">(</span>startVertex<span class="op">)</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>    visited<span class="op">[</span>startVertex<span class="op">]</span> <span class="op">=</span> <span class="ot">true</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> queue is not empty <span class="op">{</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a>        current <span class="op">:=</span> dequeue<span class="op">(</span>queue<span class="op">)</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> each edge in graph<span class="op">[</span>current<span class="op">]</span> <span class="op">{</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>            neighbor <span class="op">:=</span> edge<span class="op">.</span>Destination</span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">!</span>visited<span class="op">[</span>neighbor<span class="op">]</span> <span class="op">{</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>                mark as visited</span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>                enqueue<span class="op">(</span>neighbor<span class="op">)</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="depth-first-search-dfs" class="level3" data-number="7.2.2">
<h3 data-number="7.2.2" class="anchored" data-anchor-id="depth-first-search-dfs"><span class="header-section-number">7.2.2</span> Depth-First Search (DFS)</h3>
<p>DFS explores as far as possible along a path before backtracking. It uses a stack data structure and is useful for topological sorting, detecting cycles, or solving puzzles like mazes.</p>
<section id="example-code-4" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="example-code-4"><span class="header-section-number">*******</span> Example Code:</h4>
<div class="sourceCode" id="cb14"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> DFS<span class="op">(</span>graph <span class="kw">map</span><span class="op">[</span>Vertex<span class="op">]</span>Edges<span class="op">,</span> startVertex Vertex<span class="op">)</span> <span class="op">{</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a>    visited <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">map</span><span class="op">&lt;</span>Vertex<span class="op">]</span><span class="dt">bool</span><span class="op">)</span></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a>    stack <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span>Stack<span class="op">)</span></span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a>    push<span class="op">(</span>startVertex<span class="op">)</span></span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>    visited<span class="op">[</span>startVertex<span class="op">]</span> <span class="op">=</span> <span class="ot">true</span></span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> stack is not empty <span class="op">{</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a>        current <span class="op">:=</span> pop<span class="op">(</span>stack<span class="op">)</span></span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> each edge in graph<span class="op">[</span>current<span class="op">]</span> <span class="op">{</span></span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>            neighbor <span class="op">:=</span> edge<span class="op">.</span>Destination</span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">!</span>visited<span class="op">[</span>neighbor<span class="op">]</span> <span class="op">{</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a>                mark as visited</span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>                push<span class="op">(</span>neighbor<span class="op">)</span></span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="applications-of-graph-search-algorithms" class="level3" data-number="7.2.3">
<h3 data-number="7.2.3" class="anchored" data-anchor-id="applications-of-graph-search-algorithms"><span class="header-section-number">7.2.3</span> Applications of Graph Search Algorithms</h3>
<p>Graph search algorithms have numerous applications, including: - <strong>Shortest Path Finding</strong>: Used in GPS navigation systems to determine the shortest route between two locations. - <strong>Network Routing</strong>: Used in computer networks for routing data packets from one node to another. - <strong>Social Network Analysis</strong>: Used to analyze connections and interactions within social networks.</p>
</section>
</section>
<section id="minimum-spanning-tree-mst" class="level2" data-number="7.3">
<h2 data-number="7.3" class="anchored" data-anchor-id="minimum-spanning-tree-mst"><span class="header-section-number">7.3</span> Minimum Spanning Tree (MST)</h2>
<p>A Minimum Spanning Tree is a subset of edges that connects all vertices with the minimum possible total edge weight. It has applications in network design, clustering, and image segmentation.</p>
<section id="kruskals-algorithm" class="level3" data-number="7.3.1">
<h3 data-number="7.3.1" class="anchored" data-anchor-id="kruskals-algorithm"><span class="header-section-number">7.3.1</span> Kruskal’s Algorithm</h3>
<p>Kruskal’s algorithm works by sorting all the edges from low to high based on their weights and then adding them one by one to the MST if they don’t form a cycle. This process continues until there are (V-1) edges in the MST, where V is the number of vertices.</p>
<section id="example-code-5" class="level4" data-number="7.3.1.1">
<h4 data-number="7.3.1.1" class="anchored" data-anchor-id="example-code-5"><span class="header-section-number">7.3.1.1</span> Example Code:</h4>
<div class="sourceCode" id="cb15"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> KruskalMST<span class="op">(</span>edges <span class="op">[]</span>Edge<span class="op">,</span> vertices <span class="kw">map</span><span class="op">[</span>Vertex<span class="op">]</span><span class="kw">struct</span><span class="op">{}{})</span> <span class="op">{</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a>    sortEdges <span class="op">:=</span> sort<span class="op">(</span>edge by weight<span class="op">)</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a>    <span class="bu">make</span><span class="op">(</span>MST as empty graph<span class="op">)</span></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> each edge in sortEdges <span class="op">{</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> Find<span class="op">(</span>edge<span class="op">.</span>Source<span class="op">)</span> <span class="op">!=</span> Find<span class="op">(</span>edge<span class="op">.</span>Destination<span class="op">)</span> <span class="op">{</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>            Union<span class="op">(</span>edge<span class="op">.</span>Source<span class="op">,</span> edge<span class="op">.</span>Destination<span class="op">)</span></span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a>            AddEdge to MST</span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> MST</span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="prims-algorithm" class="level3" data-number="7.3.2">
<h3 data-number="7.3.2" class="anchored" data-anchor-id="prims-algorithm"><span class="header-section-number">7.3.2</span> Prim’s Algorithm</h3>
<p>Prim’s algorithm starts with an arbitrary vertex and adds the smallest edge that connects a new vertex to the growing MST. This process continues until all vertices are included in the MST.</p>
<section id="example-code-6" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="example-code-6"><span class="header-section-number">*******</span> Example Code:</h4>
<div class="sourceCode" id="cb16"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> PrimsMST<span class="op">(</span>vertices <span class="op">[]</span>Vertex<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">select</span> startVertex from vertices</span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a>    initialize key <span class="cf">for</span> each vertex as infinity except startVertex <span class="op">(</span>key <span class="op">=</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>    initialize parent <span class="kw">map</span></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a>    while not all vertices added <span class="op">{</span></span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a>        u <span class="op">:=</span> ExtractMinVertex<span class="op">()</span></span>
<span id="cb16-9"><a href="#cb16-9" aria-hidden="true" tabindex="-1"></a>        add u to MST</span>
<span id="cb16-10"><a href="#cb16-10" aria-hidden="true" tabindex="-1"></a>        <span class="cf">for</span> each neighbor v of u <span class="op">{</span></span>
<span id="cb16-11"><a href="#cb16-11" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> key<span class="op">[</span>v<span class="op">]</span> <span class="op">&gt;</span> weight<span class="op">(</span>u<span class="op">,</span> v<span class="op">)</span> <span class="op">{</span></span>
<span id="cb16-12"><a href="#cb16-12" aria-hidden="true" tabindex="-1"></a>                update key<span class="op">[</span>v<span class="op">]</span> and parent<span class="op">[</span>v<span class="op">]</span></span>
<span id="cb16-13"><a href="#cb16-13" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb16-14"><a href="#cb16-14" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb16-15"><a href="#cb16-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb16-16"><a href="#cb16-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-17"><a href="#cb16-17" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> MST</span>
<span id="cb16-18"><a href="#cb16-18" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
</section>
<section id="applications-of-mst" class="level3" data-number="7.3.3">
<h3 data-number="7.3.3" class="anchored" data-anchor-id="applications-of-mst"><span class="header-section-number">7.3.3</span> Applications of MST</h3>
<ul>
<li><strong>Network Design</strong>: Used to design cost-effective networks with minimal total edge weights.</li>
<li><strong>Clustering</strong>: Used in hierarchical clustering to group data points efficiently.</li>
<li><strong>Image Segmentation</strong>: Used to partition images into segments based on pixel similarities.</li>
</ul>
<hr>
<p>These sections provide a comprehensive overview of advanced tree and graph traversal techniques, as well as the construction of Minimum Spanning Trees. For further reading, you can explore recent research papers on optimized tree traversals and efficient MST algorithms for large-scale applications in Go programming.</p>


</section>
</section>
</section>

</main> <!-- /main -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
<nav class="page-navigation">
  <div class="nav-page nav-page-previous">
      <a href="../../parts/complex-data-structures/intro.html" class="pagination-link" aria-label="Complex Data Structures">
        <i class="bi bi-arrow-left-short"></i> <span class="nav-page-text">Complex Data Structures</span>
      </a>          
  </div>
  <div class="nav-page nav-page-next">
      <a href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" class="pagination-link" aria-label="Mastering Sorting and Searching Complex Data in Go">
        <span class="nav-page-text"><span class="chapter-number">7</span>&nbsp; <span class="chapter-title">Mastering Sorting and Searching Complex Data in Go</span></span> <i class="bi bi-arrow-right-short"></i>
      </a>
  </div>
</nav>
</div> <!-- /content -->




</body></html>