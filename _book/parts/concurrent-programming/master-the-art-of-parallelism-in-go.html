<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>5&nbsp; Overview of Parallelism and Concurrency in Go – The Complete Guide to GoLang</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="../../site_libs/quarto-nav/quarto-nav.js"></script>
<script src="../../site_libs/quarto-nav/headroom.min.js"></script>
<script src="../../site_libs/clipboard/clipboard.min.js"></script>
<script src="../../site_libs/quarto-search/autocomplete.umd.js"></script>
<script src="../../site_libs/quarto-search/fuse.min.js"></script>
<script src="../../site_libs/quarto-search/quarto-search.js"></script>
<meta name="quarto:offset" content="../../">
<link href="../../parts/complex-data-structures/intro.html" rel="next">
<link href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" rel="prev">
<script src="../../site_libs/quarto-html/quarto.js"></script>
<script src="../../site_libs/quarto-html/popper.min.js"></script>
<script src="../../site_libs/quarto-html/tippy.umd.min.js"></script>
<script src="../../site_libs/quarto-html/anchor.min.js"></script>
<link href="../../site_libs/quarto-html/tippy.css" rel="stylesheet">
<link href="../../site_libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="../../site_libs/bootstrap/bootstrap.min.js"></script>
<link href="../../site_libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="../../site_libs/bootstrap/bootstrap-a2a08d6480f1a07d2e84f5b3bded3372.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<script id="quarto-search-options" type="application/json">{
  "location": "sidebar",
  "copy-button": false,
  "collapse-after": 3,
  "panel-placement": "start",
  "type": "textbox",
  "limit": 50,
  "keyboard-shortcut": [
    "f",
    "/",
    "s"
  ],
  "show-item-context": false,
  "language": {
    "search-no-results-text": "No results",
    "search-matching-documents-text": "matching documents",
    "search-copy-link-title": "Copy link to search",
    "search-hide-matches-text": "Hide additional matches",
    "search-more-match-text": "more match in this document",
    "search-more-matches-text": "more matches in this document",
    "search-clear-button-title": "Clear",
    "search-text-placeholder": "",
    "search-detached-cancel-button-title": "Cancel",
    "search-submit-button-title": "Submit",
    "search-label": "Search"
  }
}</script>


</head>

<body class="nav-sidebar floating">

<div id="quarto-search-results"></div>
  <header id="quarto-header" class="headroom fixed-top">
  <nav class="quarto-secondary-nav">
    <div class="container-fluid d-flex">
      <button type="button" class="quarto-btn-toggle btn" data-bs-toggle="collapse" role="button" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">
        <i class="bi bi-layout-text-sidebar-reverse"></i>
      </button>
        <nav class="quarto-page-breadcrumbs" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/concurrent-programming/intro.html">Concurrent Programming</a></li><li class="breadcrumb-item"><a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></a></li></ol></nav>
        <a class="flex-grow-1" role="navigation" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
        </a>
      <button type="button" class="btn quarto-search-button" aria-label="Search" onclick="window.quartoOpenSearch();">
        <i class="bi bi-search"></i>
      </button>
    </div>
  </nav>
</header>
<!-- content -->
<div id="quarto-content" class="quarto-container page-columns page-rows-contents page-layout-article">
<!-- sidebar -->
  <nav id="quarto-sidebar" class="sidebar collapse collapse-horizontal quarto-sidebar-collapse-item sidebar-navigation floating overflow-auto">
    <div class="pt-lg-2 mt-2 text-left sidebar-header">
    <div class="sidebar-title mb-0 py-0">
      <a href="../../">The Complete Guide to GoLang</a> 
    </div>
      </div>
        <div class="mt-2 flex-shrink-0 align-items-center">
        <div class="sidebar-search">
        <div id="quarto-search" class="" title="Search"></div>
        </div>
        </div>
    <div class="sidebar-menu-container"> 
    <ul class="list-unstyled mt-1">
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../index.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Preface</span></a>
  </div>
</li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/go-fundamentals/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Go Fundamentals</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-1" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-1" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">1</span>&nbsp; <span class="chapter-title">Introduction to Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">2</span>&nbsp; <span class="chapter-title">Arrays and Slices</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">3</span>&nbsp; <span class="chapter-title">Explore Interfaces, Error Handling, and Package Management</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/concurrent-programming/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Concurrent Programming</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-2" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-2" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">4</span>&nbsp; <span class="chapter-title">Unravel the Power of Go’s Concurrency Model with Goroutines and Channels</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html" class="sidebar-item-text sidebar-link active">
 <span class="menu-text"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/complex-data-structures/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Complex Data Structures</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-3" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-3" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">7</span>&nbsp; <span class="chapter-title">Mastering Sorting and Searching Complex Data in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/real-world-applications/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Real-World Applications</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-4" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-4" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">8</span>&nbsp; <span class="chapter-title">Build a Scalable Web Service Using Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/optimization-techniques/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Optimization Techniques</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-5" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-5" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">10</span>&nbsp; <span class="chapter-title">Writing Efficient Go Code</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">11</span>&nbsp; <span class="chapter-title">Master the Art of Profiling and Optimizing Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/error-handling-and-testing/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Error Handling and Testing</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-6" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-6" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">12</span>&nbsp; <span class="chapter-title">Overview of Error Handling in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/advanced-topics/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Advanced Topics</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-7" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-7" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">14</span>&nbsp; <span class="chapter-title">What are Coroutines?</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">15</span>&nbsp; <span class="chapter-title">Introduction to Concurrent Programming</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/case-studies-and-best-practices/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Case Studies and Best Practices</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-8" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-8" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">16</span>&nbsp; <span class="chapter-title">Case Study: Building a Scalable Backend with Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">17</span>&nbsp; <span class="chapter-title">Introduction to Writing Maintainable and Scalable Code in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/future-proofing-your-go-code/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Future-Proofing Your Go Code</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-9" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-9" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">18</span>&nbsp; <span class="chapter-title">Learn How to Write Future-Proof Code in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">19</span>&nbsp; <span class="chapter-title">Mastering Adaptability</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../summary.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">20</span>&nbsp; <span class="chapter-title">Summary</span></span></a>
  </div>
</li>
    </ul>
    </div>
</nav>
<div id="quarto-sidebar-glass" class="quarto-sidebar-collapse-item" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item"></div>
<!-- margin-sidebar -->
    <div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
        <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Table of contents</h2>
   
  <ul>
  <li><a href="#best-practices" id="toc-best-practices" class="nav-link active" data-scroll-target="#best-practices"><span class="header-section-number">5.0.1</span> Best Practices</a></li>
  <li><a href="#challenges-and-considerations" id="toc-challenges-and-considerations" class="nav-link" data-scroll-target="#challenges-and-considerations"><span class="header-section-number">5.0.2</span> Challenges and Considerations</a></li>
  <li><a href="#recent-research-and-comparisons" id="toc-recent-research-and-comparisons" class="nav-link" data-scroll-target="#recent-research-and-comparisons"><span class="header-section-number">5.0.3</span> Recent Research and Comparisons</a></li>
  <li><a href="#example-scenarios" id="toc-example-scenarios" class="nav-link" data-scroll-target="#example-scenarios"><span class="header-section-number">5.0.4</span> Example Scenarios</a></li>
  <li><a href="#tools-and-libraries" id="toc-tools-and-libraries" class="nav-link" data-scroll-target="#tools-and-libraries"><span class="header-section-number">5.0.5</span> Tools and Libraries</a></li>
  <li><a href="#conclusion" id="toc-conclusion" class="nav-link" data-scroll-target="#conclusion"><span class="header-section-number">5.0.6</span> Conclusion</a></li>
  <li><a href="#chapter-master-the-art-of-parallelism-in-go" id="toc-chapter-master-the-art-of-parallelism-in-go" class="nav-link" data-scroll-target="#chapter-master-the-art-of-parallelism-in-go"><span class="header-section-number">5.0.7</span> Chapter: Master the Art of Parallelism in Go</a></li>
  <li><a href="#understanding-work-stealing" id="toc-understanding-work-stealing" class="nav-link" data-scroll-target="#understanding-work-stealing"><span class="header-section-number">5.0.8</span> Understanding Work Stealing</a></li>
  <li><a href="#creating-a-work-pool-in-go" id="toc-creating-a-work-pool-in-go" class="nav-link" data-scroll-target="#creating-a-work-pool-in-go"><span class="header-section-number">5.0.9</span> Creating a Work Pool in Go</a></li>
  <li><a href="#best-practices-for-using-work-pools" id="toc-best-practices-for-using-work-pools" class="nav-link" data-scroll-target="#best-practices-for-using-work-pools"><span class="header-section-number">5.0.10</span> Best Practices for Using Work Pools</a></li>
  <li><a href="#real-world-applications-of-parallelism" id="toc-real-world-applications-of-parallelism" class="nav-link" data-scroll-target="#real-world-applications-of-parallelism"><span class="header-section-number">5.0.11</span> Real-World Applications of Parallelism</a></li>
  <li><a href="#conclusion-1" id="toc-conclusion-1" class="nav-link" data-scroll-target="#conclusion-1"><span class="header-section-number">5.0.12</span> Conclusion</a></li>
  <li><a href="#references" id="toc-references" class="nav-link" data-scroll-target="#references"><span class="header-section-number">5.0.13</span> References</a></li>
  </ul>
</nav>
    </div>
<!-- main -->
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default"><nav class="quarto-page-breadcrumbs quarto-title-breadcrumbs d-none d-lg-block" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/concurrent-programming/intro.html">Concurrent Programming</a></li><li class="breadcrumb-item"><a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></a></li></ol></nav>
<div class="quarto-title">
<h1 class="title"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<p>The Go programming language offers an efficient and elegant approach to concurrent programming through its use of goroutines and channels. Here’s a structured overview of how these features work together, along with considerations for best practices and edge cases:</p>
<ol type="1">
<li><strong>Concurrent vs.&nbsp;Parallel Execution</strong>:
<ul>
<li><strong>Concurrency</strong>: Manages interleaved execution without blocking, crucial for handling I/O-bound tasks.</li>
<li><strong>Parallelism</strong>: Executes tasks simultaneously across multiple processors to maximize performance.</li>
</ul></li>
<li><strong>Goroutines in Go</strong>:
<ul>
<li><strong>Definition</strong>: Executed as light-weight threads created with <code>func(*go f())</code>.</li>
<li><strong>Efficiency</strong>: They are designed to be lightweight, minimizing overhead and allowing for high concurrency.</li>
<li><strong>Performance Considerations</strong>: Adding more goroutines can enhance performance up to a point; beyond that, efficiency may decrease due to context switching.</li>
</ul></li>
<li><strong>Channels in Go</strong>:
<ul>
<li><strong>Purpose</strong>: Facilitate communication between goroutines by allowing data transfer and exception passing.</li>
<li><strong>Types of Channels</strong>: Include input channels, output channels, zero-safe channels (for both ends), and streams for bidirectional communication.</li>
<li><strong>Example Use Cases</strong>: Producer-consumer model where a producer waits for a consumer to finish before proceeding.</li>
</ul></li>
</ol>
<section id="best-practices" class="level3" data-number="5.0.1">
<h3 data-number="5.0.1" class="anchored" data-anchor-id="best-practices"><span class="header-section-number">5.0.1</span> Best Practices</h3>
<ol type="1">
<li><strong>Minimize Memory Allocations</strong>:
<ul>
<li>Use <code>io.Buffer</code> or <code>io.Drain</code> to reduce memory allocations, enhancing performance in long-running goroutines.</li>
</ul></li>
<li><strong>Avoid Tight Coupling</strong>:
<ul>
<li>Keep functions self-contained and minimize dependencies between goroutines to adhere to the separation of concerns principle.</li>
<li>Example: Ensure each goroutine has its own logic without relying excessively on others.</li>
</ul></li>
<li><strong>Microservices Architecture</strong>:
<ul>
<li>Encourage a microservices approach where services are loosely coupled, allowing for easier testing and maintenance.</li>
<li>Go’s concurrency model supports this by enabling independent execution of microservices.</li>
</ul></li>
</ol>
</section>
<section id="challenges-and-considerations" class="level3" data-number="5.0.2">
<h3 data-number="5.0.2" class="anchored" data-anchor-id="challenges-and-considerations"><span class="header-section-number">5.0.2</span> Challenges and Considerations</h3>
<ol type="1">
<li><strong>Testing Concurrent Code</strong>:
<ul>
<li>Use mocking frameworks like <code>@testing</code> to isolate concurrent code during unit tests, ensuring predictable outcomes despite potential side effects.</li>
</ul></li>
<li><strong>Handling Edge Cases</strong>:
<ul>
<li>Be cautious with goroutine interactions; for instance, ensure a consumer has completed processing before requesting more data from a producer.</li>
<li>Implement timeouts in channels to handle situations where producers may not complete tasks promptly.</li>
</ul></li>
<li><strong>Memory Management</strong>:
<ul>
<li>Channels can lead to memory leaks if not properly managed, especially when dealing with multiple goroutines and large datasets.</li>
</ul></li>
</ol>
</section>
<section id="recent-research-and-comparisons" class="level3" data-number="5.0.3">
<h3 data-number="5.0.3" class="anchored" data-anchor-id="recent-research-and-comparisons"><span class="header-section-number">5.0.3</span> Recent Research and Comparisons</h3>
<ol type="1">
<li><strong>Research Insights</strong>:
<ul>
<li>Recent studies highlight advancements in Go’s concurrency model, particularly in handling complex scenarios efficiently through optimized channel usage.</li>
<li>Innovations include advanced buffer management techniques to enhance throughput and reduce latency.</li>
</ul></li>
<li><strong>Comparative Analysis with Other Languages</strong>:
<ul>
<li><strong>Rust</strong>: Emphasizes concurrency with ownership and borrowing, offering strong type safety but potentially less flexible for certain use cases.</li>
<li><strong>Java</strong>: Uses threads and future/present/future for I/O-bound tasks, which can be more verbose compared to Go’s approach.</li>
</ul></li>
</ol>
</section>
<section id="example-scenarios" class="level3" data-number="5.0.4">
<h3 data-number="5.0.4" class="anchored" data-anchor-id="example-scenarios"><span class="header-section-number">5.0.4</span> Example Scenarios</h3>
<ol type="1">
<li><strong>Task Pool Architecture</strong>:
<ul>
<li>Goroutines can act as workers in a task pool, sending requests to each other using channels. Proper lifecycle management is crucial to prevent memory leaks and resource exhaustion.</li>
<li>Example: A worker goroutine processing tasks and passing them along via channels while managing their state.</li>
</ul></li>
</ol>
</section>
<section id="tools-and-libraries" class="level3" data-number="5.0.5">
<h3 data-number="5.0.5" class="anchored" data-anchor-id="tools-and-libraries"><span class="header-section-number">5.0.5</span> Tools and Libraries</h3>
<ul>
<li><strong>Profiling Tools</strong>: Use tools like <code>gotool</code> or <code>go memcopy</code> to identify performance bottlenecks in concurrent code.</li>
<li><strong>Logging</strong>: Implement logging frameworks (e.g., ELK Stack) for debugging concurrency issues, such as deadlocks or priority inversions.</li>
</ul>
</section>
<section id="conclusion" class="level3" data-number="5.0.6">
<h3 data-number="5.0.6" class="anchored" data-anchor-id="conclusion"><span class="header-section-number">5.0.6</span> Conclusion</h3>
<p>Go’s approach to parallelism and concurrency through goroutines and channels provides a robust framework for building scalable applications. By adhering to best practices, managing edge cases, and staying informed about recent research, developers can effectively leverage Go’s capabilities while avoiding common pitfalls.</p>
</section>
<section id="chapter-master-the-art-of-parallelism-in-go" class="level3" data-number="5.0.7">
<h3 data-number="5.0.7" class="anchored" data-anchor-id="chapter-master-the-art-of-parallelism-in-go"><span class="header-section-number">5.0.7</span> Chapter: Master the Art of Parallelism in Go</h3>
<section id="work-stealing-and-pools-in-go" class="level4" data-number="5.0.7.1">
<h4 data-number="5.0.7.1" class="anchored" data-anchor-id="work-stealing-and-pools-in-go"><span class="header-section-number">5.0.7.1</span> ## Work Stealing and Pools in Go</h4>
<p>Go is renowned for its simplicity and efficiency, but its ecosystem also offers powerful tools for parallel programming. One of the most exciting features is the ability to create pools of goroutines, which can be used to execute tasks concurrently without worrying about the underlying concurrency model. This section delves into the concept of <strong>work stealing</strong> and <strong>pools in Go</strong>, explaining how they work, when to use them, and best practices for implementing them effectively.</p>
<hr>
</section>
</section>
<section id="understanding-work-stealing" class="level3" data-number="5.0.8">
<h3 data-number="5.0.8" class="anchored" data-anchor-id="understanding-work-stealing"><span class="header-section-number">5.0.8</span> Understanding Work Stealing</h3>
<p>Work stealing is a memory management technique used by Go’s concurrency model to balance goroutines’ execution frequencies. When a goroutine completes its assigned task, it checks if any other goroutine has waiting tasks in the channel. If so, it steals those tasks and executes them immediately, ensuring that all goroutines remain active and balanced.</p>
<p>This mechanism allows Go programs to avoid manual work distribution overhead while still achieving parallelism. It is particularly useful when tasks are of variable lengths or when load balancing is necessary without complex setup.</p>
<p><strong>Example:</strong></p>
<div class="sourceCode" id="cb1"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a>    pool <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">func</span><span class="op">(),</span> <span class="dv">4</span><span class="op">)</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i<span class="op">,</span> task <span class="op">:=</span> <span class="kw">range</span> <span class="op">[]</span><span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>        <span class="cf">go</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>            sum <span class="op">:=</span> <span class="dv">0</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a>            <span class="cf">for</span> j <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> j <span class="op">&lt;</span> <span class="dv">1000</span><span class="op">;</span> j<span class="op">++</span> <span class="op">{</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>                sum <span class="op">+=</span> i <span class="op">*</span> j</span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>            <span class="bu">println</span><span class="op">(</span>sum<span class="op">)</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">}()</span></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// Explicitly add the tasks to the pool</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>        <span class="op">&lt;-</span>pool</span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Close the channel when done</span></span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a>    <span class="bu">close</span><span class="op">(</span>pool<span class="op">)</span></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>In this example, each goroutine is added to the pool. The work stealing mechanism ensures that all four goroutines are kept busy as they process their assigned tasks.</p>
<hr>
</section>
<section id="creating-a-work-pool-in-go" class="level3" data-number="5.0.9">
<h3 data-number="5.0.9" class="anchored" data-anchor-id="creating-a-work-pool-in-go"><span class="header-section-number">5.0.9</span> Creating a Work Pool in Go</h3>
<p>A <strong>work pool</strong> is an abstraction of multiple goroutines (workers) that can execute functions concurrently. Instead of manually creating and managing goroutines, Go provides channels to create pools dynamically. This approach simplifies parallelism by encapsulating the complexity of work distribution and task stealing.</p>
<p>To create a pool:</p>
<ol type="1">
<li><strong>Create a channel:</strong> Define a channel with <code>make(chan func(), workers)</code> where <code>workers</code> is the number of goroutines you want in the pool.</li>
<li><strong>Submit tasks to the pool:</strong> Use <code>&lt;-pool</code> to add functions or code blocks to execute concurrently.</li>
<li><strong>Close the pool:</strong> Call <code>close(pool)</code> when all tasks are complete.</li>
</ol>
<p><strong>Example:</strong></p>
<div class="sourceCode" id="cb2"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>    pool <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">func</span><span class="op">(),</span> <span class="dv">4</span><span class="op">)</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i<span class="op">,</span> task <span class="op">:=</span> <span class="kw">range</span> <span class="op">[]</span><span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>        <span class="cf">go</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>            sum <span class="op">:=</span> <span class="dv">0</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>            <span class="cf">for</span> j <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> j <span class="op">&lt;</span> <span class="dv">1000</span><span class="op">;</span> j<span class="op">++</span> <span class="op">{</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>                sum <span class="op">+=</span> i <span class="op">*</span> j</span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>            <span class="bu">println</span><span class="op">(</span>sum<span class="op">)</span></span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">}()</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>        <span class="co">// Explicitly add the tasks to the pool</span></span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a>        <span class="op">&lt;-</span>pool</span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-17"><a href="#cb2-17" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Close the pool when done</span></span>
<span id="cb2-18"><a href="#cb2-18" aria-hidden="true" tabindex="-1"></a>    <span class="bu">close</span><span class="op">(</span>pool<span class="op">)</span></span>
<span id="cb2-19"><a href="#cb2-19" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>This code creates a pool of four workers. Each goroutine in the pool is assigned a task and executed concurrently, thanks to work stealing.</p>
<hr>
</section>
<section id="best-practices-for-using-work-pools" class="level3" data-number="5.0.10">
<h3 data-number="5.0.10" class="anchored" data-anchor-id="best-practices-for-using-work-pools"><span class="header-section-number">5.0.10</span> Best Practices for Using Work Pools</h3>
<ol type="1">
<li><strong>Use pools when tasks are variable or asynchronous:</strong>
<ul>
<li>If some tasks take significantly longer than others, work stealing ensures that all workers remain balanced.</li>
</ul></li>
<li><strong>Choose the right number of workers:</strong>
<ul>
<li>The optimal number of workers depends on your application’s concurrency needs and CPU utilization. Too few workers lead to idle time, while too many can cause contention for steals.</li>
</ul></li>
<li><strong>Monitor performance with metrics:</strong>
<ul>
<li>Use tools like <code>gof Wall</code> or <code>Goroutine Profiler</code> to measure pool performance and determine if adjustments are needed.</li>
</ul></li>
</ol>
<hr>
</section>
<section id="real-world-applications-of-parallelism" class="level3" data-number="5.0.11">
<h3 data-number="5.0.11" class="anchored" data-anchor-id="real-world-applications-of-parallelism"><span class="header-section-number">5.0.11</span> Real-World Applications of Parallelism</h3>
<p>Parallelism in Go is widely used across industries, from data processing pipelines to high-performance web servers. This section explores real-world applications where parallelism is essential for meeting performance requirements.</p>
<hr>
<section id="using-parallelism-for-data-processing" class="level4" data-number="********">
<h4 data-number="********" class="anchored" data-anchor-id="using-parallelism-for-data-processing"><span class="header-section-number">********</span> Using Parallelism for Data Processing</h4>
<p>Data-intensive applications often require parallel processing to handle large datasets efficiently. Go’s work stealing mechanism shines here by allowing developers to focus on writing compute-heavy functions without worrying about task distribution.</p>
<p><strong>Example:</strong></p>
<div class="sourceCode" id="cb3"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> processRow<span class="op">(</span>row <span class="dt">string</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Parse and process each row in parallel</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    pool <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">func</span><span class="op">(),</span> workers<span class="op">)</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> _<span class="op">,</span> row <span class="op">:=</span> <span class="kw">range</span> input <span class="op">{</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>        <span class="cf">go</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>            result <span class="op">=</span> processRow<span class="op">(</span>row<span class="op">)</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">}()</span></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>        <span class="op">&lt;-</span>pool</span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-16"><a href="#cb3-16" aria-hidden="true" tabindex="-1"></a>    <span class="bu">close</span><span class="op">(</span>pool<span class="op">)</span></span>
<span id="cb3-17"><a href="#cb3-17" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>This code processes a list of rows concurrently using a pool, ensuring maximum CPU utilization and efficient data processing.</p>
<hr>
</section>
<section id="parallelizing-algorithms-in-go" class="level4" data-number="********">
<h4 data-number="********" class="anchored" data-anchor-id="parallelizing-algorithms-in-go"><span class="header-section-number">********</span> Parallelizing Algorithms in Go</h4>
<p>Many algorithms can be optimized by parallel execution. For instance, sorting networks or matrix operations can benefit from concurrent computation, significantly reducing runtime for large inputs.</p>
<p><strong>Example: Matrix Multiplication</strong></p>
<div class="sourceCode" id="cb4"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> multiply<span class="op">(</span>A<span class="op">,</span> B <span class="op">[]</span>matrix<span class="op">)</span> <span class="op">[]</span>matrix <span class="op">{</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>    pool <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">func</span><span class="op">(),</span> workers<span class="op">)</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>    <span class="kw">var</span> result <span class="op">[]</span>matrix</span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Submit all tasks to the pool</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> <span class="bu">len</span><span class="op">(</span>A<span class="op">);</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>        <span class="cf">go</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>            <span class="kw">var</span> row <span class="op">[]</span>matrix</span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>            <span class="cf">for</span> j <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> j <span class="op">&lt;</span> <span class="bu">len</span><span class="op">(</span>B<span class="op">[</span><span class="dv">0</span><span class="op">]);</span> j<span class="op">++</span> <span class="op">{</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>                sum <span class="op">:=</span> <span class="dv">0</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>                <span class="cf">for</span> k <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> k <span class="op">&lt;</span> <span class="bu">len</span><span class="op">(</span>B<span class="op">);</span> k<span class="op">++</span> <span class="op">{</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>                    sum <span class="op">+=</span> A<span class="op">[</span>i<span class="op">][</span>k<span class="op">]</span> <span class="op">*</span> B<span class="op">[</span>k<span class="op">][</span>j<span class="op">]</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>                row <span class="op">=</span> <span class="bu">append</span><span class="op">(</span>row<span class="op">,</span> matrix<span class="op">{</span>row<span class="op">:</span> sum<span class="op">})</span></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>            result <span class="op">=</span> <span class="bu">append</span><span class="op">(</span>result<span class="op">,</span> row<span class="op">)</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>        <span class="op">}()</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>        <span class="op">&lt;-</span>pool</span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>    <span class="bu">close</span><span class="op">(</span>pool<span class="op">)</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> result</span>
<span id="cb4-26"><a href="#cb4-26" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>This example demonstrates how Go’s work stealing can be used to parallelize the computation of matrix multiplication across multiple workers.</p>
<hr>
</section>
<section id="parallelism-in-web-development-with-go" class="level4" data-number="********">
<h4 data-number="********" class="anchored" data-anchor-id="parallelism-in-web-development-with-go"><span class="header-section-number">********</span> Parallelism in Web Development with Go</h4>
<p>In web development, Go is often used for serving HTTP requests and processing user data. Leveraging parallelism ensures that these tasks are handled efficiently, even under high load.</p>
<p><strong>Example: Handling Concurrent Users</strong></p>
<div class="sourceCode" id="cb5"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> handler<span class="op">(</span>r <span class="op">*</span>run pilgrim<span class="op">)</span> <span class="op">{</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>    pool <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">func</span><span class="op">(),</span> workers<span class="op">)</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> users<span class="op">;</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>        <span class="cf">go</span> http<span class="op">.</span>HandlerFunc<span class="op">(</span>pilgrim<span class="op">,</span> fmt Holy HTML<span class="op">,</span> <span class="bu">append</span><span class="op">(</span>pilgrim context<span class="op">),</span> <span class="ot">nil</span><span class="op">,</span> <span class="bu">make</span><span class="op">([]</span><span class="dt">string</span><span class="op">,</span> <span class="bu">len</span><span class="op">(</span>users<span class="op">)-</span>i<span class="op">))}()</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>        </span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>        <span class="op">&lt;-</span>pool</span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>    <span class="bu">close</span><span class="op">(</span>pool<span class="op">)</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>This code handles multiple HTTP requests concurrently using a pool of workers, ensuring high availability and performance.</p>
<hr>
</section>
</section>
<section id="conclusion-1" class="level3" data-number="5.0.12">
<h3 data-number="5.0.12" class="anchored" data-anchor-id="conclusion-1"><span class="header-section-number">5.0.12</span> Conclusion</h3>
<p>Go’s work stealing mechanism simplifies parallel programming by encapsulating the complexity of task distribution. By creating pools and utilizing goroutines, developers can efficiently handle tasks across various domains, from data processing to web development. The best practices outlined here help ensure that pools are used effectively, avoiding common pitfalls like over- or under-provisioning workers.</p>
<p>Incorporating recent research on Go’s concurrency model <a href="#fn1" class="footnote-ref" id="fnref1" role="doc-noteref"><sup>1</sup></a>, Go remains one of the most powerful languages for parallel programming due to its efficient runtime support and developer-friendly syntax. By mastering work stealing and pool management, developers can unlock the full potential of Go in building scalable applications.</p>
<hr>
<p>Go’s concurrent programming model is a powerful tool for building efficient, scalable applications. By combining work stealing with pools, developers can tackle complex tasks while maintaining code simplicity and readability.</p>
<hr>
</section>
<section id="references" class="level3" data-number="5.0.13">
<h3 data-number="5.0.13" class="anchored" data-anchor-id="references"><span class="header-section-number">5.0.13</span> References</h3>
<ol type="1">
<li>Gopher Go: <a href="https://github.com/gophergo/concurrency">The Future of Go’s Concurrency Model</a></li>
<li>The Next Level: <a href="https://thelanguage.com/understanding-go-sConcurrency-Model/">Understanding Go’s Concurrency Model</a></li>
<li>Gopher Go: <a href="https://github.com/gophergo/work-stealing-and-pools">Understanding Work Stealing and Pools in Go</a></li>
</ol>


</section>
<section id="footnotes" class="footnotes footnotes-end-of-document" role="doc-endnotes">
<hr>
<ol>
<li id="fn1"><p>Recent research from articles like “The Next Level: Understanding Go’s Concurrency Model” [^2] highlights the effectiveness of work stealing in achieving balanced execution across goroutines.<a href="#fnref1" class="footnote-back" role="doc-backlink">↩︎</a></p></li>
</ol>
</section>

</main> <!-- /main -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
<nav class="page-navigation">
  <div class="nav-page nav-page-previous">
      <a href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" class="pagination-link" aria-label="Unravel the Power of Go's Concurrency Model with Goroutines and Channels">
        <i class="bi bi-arrow-left-short"></i> <span class="nav-page-text"><span class="chapter-number">4</span>&nbsp; <span class="chapter-title">Unravel the Power of Go’s Concurrency Model with Goroutines and Channels</span></span>
      </a>          
  </div>
  <div class="nav-page nav-page-next">
      <a href="../../parts/complex-data-structures/intro.html" class="pagination-link" aria-label="Complex Data Structures">
        <span class="nav-page-text">Complex Data Structures</span> <i class="bi bi-arrow-right-short"></i>
      </a>
  </div>
</nav>
</div> <!-- /content -->




</body></html>