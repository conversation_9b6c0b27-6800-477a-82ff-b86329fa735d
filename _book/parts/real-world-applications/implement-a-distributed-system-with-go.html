<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>9&nbsp; Chapter 7: Implement a Distributed System with Go – The Complete Guide to GoLang</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="../../site_libs/quarto-nav/quarto-nav.js"></script>
<script src="../../site_libs/quarto-nav/headroom.min.js"></script>
<script src="../../site_libs/clipboard/clipboard.min.js"></script>
<script src="../../site_libs/quarto-search/autocomplete.umd.js"></script>
<script src="../../site_libs/quarto-search/fuse.min.js"></script>
<script src="../../site_libs/quarto-search/quarto-search.js"></script>
<meta name="quarto:offset" content="../../">
<link href="../../parts/optimization-techniques/intro.html" rel="next">
<link href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" rel="prev">
<script src="../../site_libs/quarto-html/quarto.js"></script>
<script src="../../site_libs/quarto-html/popper.min.js"></script>
<script src="../../site_libs/quarto-html/tippy.umd.min.js"></script>
<script src="../../site_libs/quarto-html/anchor.min.js"></script>
<link href="../../site_libs/quarto-html/tippy.css" rel="stylesheet">
<link href="../../site_libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="../../site_libs/bootstrap/bootstrap.min.js"></script>
<link href="../../site_libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="../../site_libs/bootstrap/bootstrap-a2a08d6480f1a07d2e84f5b3bded3372.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<script id="quarto-search-options" type="application/json">{
  "location": "sidebar",
  "copy-button": false,
  "collapse-after": 3,
  "panel-placement": "start",
  "type": "textbox",
  "limit": 50,
  "keyboard-shortcut": [
    "f",
    "/",
    "s"
  ],
  "show-item-context": false,
  "language": {
    "search-no-results-text": "No results",
    "search-matching-documents-text": "matching documents",
    "search-copy-link-title": "Copy link to search",
    "search-hide-matches-text": "Hide additional matches",
    "search-more-match-text": "more match in this document",
    "search-more-matches-text": "more matches in this document",
    "search-clear-button-title": "Clear",
    "search-text-placeholder": "",
    "search-detached-cancel-button-title": "Cancel",
    "search-submit-button-title": "Submit",
    "search-label": "Search"
  }
}</script>


</head>

<body class="nav-sidebar floating">

<div id="quarto-search-results"></div>
  <header id="quarto-header" class="headroom fixed-top">
  <nav class="quarto-secondary-nav">
    <div class="container-fluid d-flex">
      <button type="button" class="quarto-btn-toggle btn" data-bs-toggle="collapse" role="button" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">
        <i class="bi bi-layout-text-sidebar-reverse"></i>
      </button>
        <nav class="quarto-page-breadcrumbs" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/real-world-applications/intro.html">Real-World Applications</a></li><li class="breadcrumb-item"><a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></a></li></ol></nav>
        <a class="flex-grow-1" role="navigation" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
        </a>
      <button type="button" class="btn quarto-search-button" aria-label="Search" onclick="window.quartoOpenSearch();">
        <i class="bi bi-search"></i>
      </button>
    </div>
  </nav>
</header>
<!-- content -->
<div id="quarto-content" class="quarto-container page-columns page-rows-contents page-layout-article">
<!-- sidebar -->
  <nav id="quarto-sidebar" class="sidebar collapse collapse-horizontal quarto-sidebar-collapse-item sidebar-navigation floating overflow-auto">
    <div class="pt-lg-2 mt-2 text-left sidebar-header">
    <div class="sidebar-title mb-0 py-0">
      <a href="../../">The Complete Guide to GoLang</a> 
    </div>
      </div>
        <div class="mt-2 flex-shrink-0 align-items-center">
        <div class="sidebar-search">
        <div id="quarto-search" class="" title="Search"></div>
        </div>
        </div>
    <div class="sidebar-menu-container"> 
    <ul class="list-unstyled mt-1">
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../index.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Preface</span></a>
  </div>
</li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/go-fundamentals/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Go Fundamentals</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-1" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-1" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">1</span>&nbsp; <span class="chapter-title">Introduction to Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">2</span>&nbsp; <span class="chapter-title">Arrays and Slices</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">3</span>&nbsp; <span class="chapter-title">Explore Interfaces, Error Handling, and Package Management</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/concurrent-programming/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Concurrent Programming</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-2" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-2" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">4</span>&nbsp; <span class="chapter-title">Unravel the Power of Go’s Concurrency Model with Goroutines and Channels</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/complex-data-structures/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Complex Data Structures</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-3" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-3" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">7</span>&nbsp; <span class="chapter-title">Mastering Sorting and Searching Complex Data in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/real-world-applications/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Real-World Applications</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-4" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-4" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">8</span>&nbsp; <span class="chapter-title">Build a Scalable Web Service Using Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html" class="sidebar-item-text sidebar-link active">
 <span class="menu-text"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/optimization-techniques/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Optimization Techniques</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-5" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-5" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">10</span>&nbsp; <span class="chapter-title">Writing Efficient Go Code</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">11</span>&nbsp; <span class="chapter-title">Master the Art of Profiling and Optimizing Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/error-handling-and-testing/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Error Handling and Testing</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-6" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-6" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">12</span>&nbsp; <span class="chapter-title">Overview of Error Handling in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/advanced-topics/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Advanced Topics</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-7" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-7" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">14</span>&nbsp; <span class="chapter-title">What are Coroutines?</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">15</span>&nbsp; <span class="chapter-title">Introduction to Concurrent Programming</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/case-studies-and-best-practices/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Case Studies and Best Practices</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-8" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-8" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">16</span>&nbsp; <span class="chapter-title">Case Study: Building a Scalable Backend with Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">17</span>&nbsp; <span class="chapter-title">Introduction to Writing Maintainable and Scalable Code in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/future-proofing-your-go-code/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Future-Proofing Your Go Code</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-9" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-9" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">18</span>&nbsp; <span class="chapter-title">Learn How to Write Future-Proof Code in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">19</span>&nbsp; <span class="chapter-title">Mastering Adaptability</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../summary.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">20</span>&nbsp; <span class="chapter-title">Summary</span></span></a>
  </div>
</li>
    </ul>
    </div>
</nav>
<div id="quarto-sidebar-glass" class="quarto-sidebar-collapse-item" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item"></div>
<!-- margin-sidebar -->
    <div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
        <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Table of contents</h2>
   
  <ul>
  <li><a href="#introduction-to-distributed-systems" id="toc-introduction-to-distributed-systems" class="nav-link active" data-scroll-target="#introduction-to-distributed-systems"><span class="header-section-number">9.1</span> 7.1 Introduction to Distributed Systems</a>
  <ul class="collapse">
  <li><a href="#what-are-distributed-systems" id="toc-what-are-distributed-systems" class="nav-link" data-scroll-target="#what-are-distributed-systems"><span class="header-section-number">9.1.1</span> What Are Distributed Systems?</a></li>
  <li><a href="#benefits-of-distributed-systems" id="toc-benefits-of-distributed-systems" class="nav-link" data-scroll-target="#benefits-of-distributed-systems"><span class="header-section-number">9.1.2</span> Benefits of Distributed Systems</a></li>
  <li><a href="#challenges-in-implementing-distributed-systems" id="toc-challenges-in-implementing-distributed-systems" class="nav-link" data-scroll-target="#challenges-in-implementing-distributed-systems"><span class="header-section-number">9.1.3</span> Challenges in Implementing Distributed Systems</a></li>
  </ul></li>
  <li><a href="#go-language-fundamentals" id="toc-go-language-fundamentals" class="nav-link" data-scroll-target="#go-language-fundamentals"><span class="header-section-number">9.2</span> 7.2 Go Language Fundamentals</a>
  <ul class="collapse">
  <li><a href="#go-syntax-and-basics" id="toc-go-syntax-and-basics" class="nav-link" data-scroll-target="#go-syntax-and-basics"><span class="header-section-number">9.2.1</span> Go Syntax and Basics</a></li>
  <li><a href="#error-handling-and-logging" id="toc-error-handling-and-logging" class="nav-link" data-scroll-target="#error-handling-and-logging"><span class="header-section-number">9.2.2</span> Error Handling and Logging</a></li>
  <li><a href="#concurrency-in-go" id="toc-concurrency-in-go" class="nav-link" data-scroll-target="#concurrency-in-go"><span class="header-section-number">9.2.3</span> Concurrency in Go</a></li>
  </ul></li>
  <li><a href="#designing-a-distributed-system-with-go" id="toc-designing-a-distributed-system-with-go" class="nav-link" data-scroll-target="#designing-a-distributed-system-with-go"><span class="header-section-number">9.3</span> 7.3 Designing a Distributed System with Go</a>
  <ul class="collapse">
  <li><a href="#defining-the-system-architecture" id="toc-defining-the-system-architecture" class="nav-link" data-scroll-target="#defining-the-system-architecture"><span class="header-section-number">9.3.1</span> Defining the System Architecture</a></li>
  <li><a href="#choosing-a-communication-protocol" id="toc-choosing-a-communication-protocol" class="nav-link" data-scroll-target="#choosing-a-communication-protocol"><span class="header-section-number">9.3.2</span> Choosing a Communication Protocol</a></li>
  <li><a href="#handling-network-latency-and-failures" id="toc-handling-network-latency-and-failures" class="nav-link" data-scroll-target="#handling-network-latency-and-failures"><span class="header-section-number">9.3.3</span> Handling Network Latency and Failures</a></li>
  <li><a href="#implementing-a-simple-distributed-system" id="toc-implementing-a-simple-distributed-system" class="nav-link" data-scroll-target="#implementing-a-simple-distributed-system"><span class="header-section-number">9.3.4</span> Implementing a Simple Distributed System</a></li>
  </ul></li>
  <li><a href="#best-practices-and-recent-research" id="toc-best-practices-and-recent-research" class="nav-link" data-scroll-target="#best-practices-and-recent-research"><span class="header-section-number">9.4</span> 7.4 Best Practices and Recent Research</a>
  <ul class="collapse">
  <li><a href="#communication-mechanisms" id="toc-communication-mechanisms" class="nav-link" data-scroll-target="#communication-mechanisms"><span class="header-section-number">9.4.1</span> 1. Communication Mechanisms</a></li>
  <li><a href="#message-queues-and-pubsub-models" id="toc-message-queues-and-pubsub-models" class="nav-link" data-scroll-target="#message-queues-and-pubsub-models"><span class="header-section-number">9.4.2</span> 2. Message Queues and Pub/Sub Models</a></li>
  <li><a href="#handling-node-failures-and-recovery" id="toc-handling-node-failures-and-recovery" class="nav-link" data-scroll-target="#handling-node-failures-and-recovery"><span class="header-section-number">9.4.3</span> 3. Handling Node Failures and Recovery</a></li>
  <li><a href="#synchronizing-data" id="toc-synchronizing-data" class="nav-link" data-scroll-target="#synchronizing-data"><span class="header-section-number">9.4.4</span> 4. Synchronizing Data</a></li>
  <li><a href="#recent-research-and-best-practices" id="toc-recent-research-and-best-practices" class="nav-link" data-scroll-target="#recent-research-and-best-practices"><span class="header-section-number">9.4.5</span> 5. Recent Research and Best Practices</a></li>
  <li><a href="#example-code" id="toc-example-code" class="nav-link" data-scroll-target="#example-code"><span class="header-section-number">9.4.6</span> Example Code</a></li>
  <li><a href="#implementing-a-distributed-system-with-go" id="toc-implementing-a-distributed-system-with-go" class="nav-link" data-scroll-target="#implementing-a-distributed-system-with-go"><span class="header-section-number">9.4.7</span> Implementing a Distributed System with Go</a></li>
  </ul></li>
  </ul>
</nav>
    </div>
<!-- main -->
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default"><nav class="quarto-page-breadcrumbs quarto-title-breadcrumbs d-none d-lg-block" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/real-world-applications/intro.html">Real-World Applications</a></li><li class="breadcrumb-item"><a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></a></li></ol></nav>
<div class="quarto-title">
<h1 class="title"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<p>Distributed systems are collections of independent computers (nodes) that work together to achieve a common goal. These systems are designed to handle tasks that are too large or complex for a single machine, providing scalability, fault tolerance, and improved performance. This chapter explores how to implement a distributed system using Go, leveraging its unique features such as concurrency, simplicity, and robust error handling.</p>
<hr>
<section id="introduction-to-distributed-systems" class="level2" data-number="9.1">
<h2 data-number="9.1" class="anchored" data-anchor-id="introduction-to-distributed-systems"><span class="header-section-number">9.1</span> 7.1 Introduction to Distributed Systems</h2>
<section id="what-are-distributed-systems" class="level3" data-number="9.1.1">
<h3 data-number="9.1.1" class="anchored" data-anchor-id="what-are-distributed-systems"><span class="header-section-number">9.1.1</span> What Are Distributed Systems?</h3>
<p>A distributed system consists of multiple nodes (servers, clients, or workers) that communicate over a network to accomplish a shared objective. These systems are designed to handle tasks like database replication, load balancing, task distribution, and service availability. They operate under the principles of fault tolerance, scalability, and decoupling.</p>
<p>Examples of distributed systems include cloud platforms like AWS, Kubernetes, and Docker Swarm, as well as microservices architectures such as Google’s Gopher and Akka. Go (Golang) is particularly well-suited for building these systems due to its concurrent model, built-in support for fault tolerance, and efficient networking capabilities.</p>
</section>
<section id="benefits-of-distributed-systems" class="level3" data-number="9.1.2">
<h3 data-number="9.1.2" class="anchored" data-anchor-id="benefits-of-distributed-systems"><span class="header-section-number">9.1.2</span> Benefits of Distributed Systems</h3>
<p>The benefits of distributed systems include:</p>
<ol type="1">
<li><strong>Scalability</strong>: Adding more nodes can improve performance without affecting existing functionality.</li>
<li><strong>Fault Tolerance</strong>: If one node fails, others can take over, ensuring system availability.</li>
<li><strong>Distributing Workload</strong>: Tasks are divided among multiple nodes, reducing processing time and improving throughput.</li>
<li><strong>Enhanced Security</strong>: Data is encrypted in transit or at rest, depending on the implementation.</li>
</ol>
</section>
<section id="challenges-in-implementing-distributed-systems" class="level3" data-number="9.1.3">
<h3 data-number="9.1.3" class="anchored" data-anchor-id="challenges-in-implementing-distributed-systems"><span class="header-section-number">9.1.3</span> Challenges in Implementing Distributed Systems</h3>
<p>Implementing distributed systems presents several challenges:</p>
<ol type="1">
<li><strong>Network Latency</strong>: Delays caused by slow network connections can degrade system performance.</li>
<li><strong>Consistency</strong>: Ensuring all nodes have consistent data states despite node failures is challenging.</li>
<li><strong>Security Risks</strong>: Attacks such as Sybil attacks and Denial of Service (DoS) can compromise system integrity.</li>
<li><strong>Complex Interoperability</strong>: Integrating different systems, protocols, and technologies requires careful design.</li>
</ol>
<hr>
</section>
</section>
<section id="go-language-fundamentals" class="level2" data-number="9.2">
<h2 data-number="9.2" class="anchored" data-anchor-id="go-language-fundamentals"><span class="header-section-number">9.2</span> 7.2 Go Language Fundamentals</h2>
<section id="go-syntax-and-basics" class="level3" data-number="9.2.1">
<h3 data-number="9.2.1" class="anchored" data-anchor-id="go-syntax-and-basics"><span class="header-section-number">9.2.1</span> Go Syntax and Basics</h3>
<p>Go is a statically typed, compiled language that emphasizes simplicity, efficiency, and scalability. Its key features include:</p>
<ul>
<li><strong>Concurrent Execution</strong>: Go’s lightweight concurrency model (using goroutines) allows multiple I/O-bound tasks to run simultaneously without blocking the CPU.</li>
<li><strong>Error Handling</strong>: Errors are first-class citizens in Go; they can be handled explicitly using error and handle types.</li>
<li><strong>Logging</strong>: The <code>log</code> package provides a flexible way to log messages at various levels of detail.</li>
</ul>
</section>
<section id="error-handling-and-logging" class="level3" data-number="9.2.2">
<h3 data-number="9.2.2" class="anchored" data-anchor-id="error-handling-and-logging"><span class="header-section-number">9.2.2</span> Error Handling and Logging</h3>
<p>Go’s approach to error handling involves defining an interface with zero implementation. This allows for type-safe error handling without the overhead of exception objects. For logging, Go offers the <code>logger</code> package, which can write logs in different formats (e.g., console, file, or database) based on configuration.</p>
<div class="sourceCode" id="cb1"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example of error handling</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> MyFunction<span class="op">()</span> <span class="op">{</span></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a>    err <span class="op">:=</span> someFunction<span class="op">()</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> err <span class="op">!=</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>        handleError<span class="op">(</span>err<span class="op">)</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> handleError<span class="op">(</span>err <span class="dt">error</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>    log<span class="op">.</span>Printf<span class="op">(</span><span class="st">"Error: %v"</span><span class="op">,</span> err<span class="op">)</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="concurrency-in-go" class="level3" data-number="9.2.3">
<h3 data-number="9.2.3" class="anchored" data-anchor-id="concurrency-in-go"><span class="header-section-number">9.2.3</span> Concurrency in Go</h3>
<p>Go’s concurrency model simplifies writing multi-threaded programs using goroutines and channels. Goroutines are preemptively scheduled, allowing the current goroutine to pause execution when yielding control to another goroutine.</p>
<p>Channels enable inter-concurrency communication by sending values between goroutines. They can be used for producer-consumer patterns, message passing, or complex event-driven architectures.</p>
<div class="sourceCode" id="cb2"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example of concurrency using channels</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Create a channel and two goroutines waiting in it</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>    c <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="dt">string</span><span class="op">,</span> <span class="dv">2</span><span class="op">)</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    g1 <span class="op">=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span> <span class="op">&lt;-</span>c<span class="op">;</span> <span class="op">}</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>    g2 <span class="op">=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span> <span class="op">&lt;-</span>c<span class="op">;</span> <span class="op">}</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Start the goroutines</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>    start<span class="op">(</span>g1<span class="op">)</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>    start<span class="op">(</span>g2<span class="op">)</span></span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<hr>
</section>
</section>
<section id="designing-a-distributed-system-with-go" class="level2" data-number="9.3">
<h2 data-number="9.3" class="anchored" data-anchor-id="designing-a-distributed-system-with-go"><span class="header-section-number">9.3</span> 7.3 Designing a Distributed System with Go</h2>
<section id="defining-the-system-architecture" class="level3" data-number="9.3.1">
<h3 data-number="9.3.1" class="anchored" data-anchor-id="defining-the-system-architecture"><span class="header-section-number">9.3.1</span> Defining the System Architecture</h3>
<p>The first step in designing a distributed system is defining its architecture. Key considerations include:</p>
<ul>
<li><strong>Node Types</strong>: The roles of nodes (e.g., master, worker, client).</li>
<li><strong>Data Distribution</strong>: Where data will be stored and how it will be accessed.</li>
<li><strong>Communication Protocol</strong>: The method nodes use to exchange messages (e.g., HTTP, gRPC).</li>
</ul>
</section>
<section id="choosing-a-communication-protocol" class="level3" data-number="9.3.2">
<h3 data-number="9.3.2" class="anchored" data-anchor-id="choosing-a-communication-protocol"><span class="header-section-number">9.3.2</span> Choosing a Communication Protocol</h3>
<p>Selecting the right communication protocol is crucial for system design. Popular options include:</p>
<ol type="1">
<li><strong>gRPC</strong>: A high-performance, open-source protocol designed for distributed systems with built-in support for authentication and load balancing.</li>
<li><strong>HTTP</strong>: The standard protocol for web services; simple but not optimized for high throughput.</li>
</ol>
<p>Go’s simplicity and robust error handling make it a good choice for implementing these protocols. For instance, writing a client that connects to a gRPC server is straightforward:</p>
<div class="sourceCode" id="cb3"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example of connecting to a gRPC server</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>    <span class="op">/</span>grpc</span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    <span class="op">/</span>grpc<span class="op">.</span>io<span class="op">/</span>v1</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Create a channel</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>    c <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="op">*</span>v1<span class="op">.</span>Server<span class="op">,</span> <span class="dv">3</span><span class="op">)</span></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Start the server (replace with actual server address)</span></span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a>    s <span class="op">:=</span> <span class="op">&amp;</span>v1<span class="op">.</span>Server<span class="op">{}</span></span>
<span id="cb3-15"><a href="#cb3-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">&lt;-</span>c<span class="op">(</span>s<span class="op">)</span></span>
<span id="cb3-16"><a href="#cb3-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-17"><a href="#cb3-17" aria-hidden="true" tabindex="-1"></a>    clientClient <span class="op">:=</span> <span class="op">&amp;</span>v1<span class="op">.</span>Client<span class="op">{}</span></span>
<span id="cb3-18"><a href="#cb3-18" aria-hidden="true" tabindex="-1"></a>    _<span class="op">,</span> ok <span class="op">:=</span> clientClient<span class="op">.</span>Connect<span class="op">(</span>c<span class="op">)</span></span>
<span id="cb3-19"><a href="#cb3-19" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">!</span>ok <span class="op">{</span></span>
<span id="cb3-20"><a href="#cb3-20" aria-hidden="true" tabindex="-1"></a>        log<span class="op">.</span>Fatal<span class="op">(</span><span class="st">"Failed to connect"</span><span class="op">)</span></span>
<span id="cb3-21"><a href="#cb3-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-22"><a href="#cb3-22" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="handling-network-latency-and-failures" class="level3" data-number="9.3.3">
<h3 data-number="9.3.3" class="anchored" data-anchor-id="handling-network-latency-and-failures"><span class="header-section-number">9.3.3</span> Handling Network Latency and Failures</h3>
<p>Network latency can cause delays in communication between nodes. To handle this, systems often implement mechanisms like timeouts or retries.</p>
<p>Go’s concurrency model allows for efficient implementation of fault tolerance using replicated state and consensus algorithms such as Raft or Paxos. For example, a simple client waiting for a response might wait for multiple nodes to confirm their responses before proceeding:</p>
<div class="sourceCode" id="cb4"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example of handling network latency with retries</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> handleRequest<span class="op">(</span>timeout <span class="dt">int</span><span class="op">)</span> <span class="op">{</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>    <span class="kw">var</span> responses <span class="op">[]</span><span class="dt">string</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> maxRetries<span class="op">;</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>        start <span class="op">:=</span> time<span class="op">.</span>Now<span class="op">()</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> err<span class="op">,</span> ok <span class="op">:=</span> request<span class="op">(&amp;</span>server<span class="op">);</span> ok <span class="op">{</span></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>            response<span class="op">,</span> ok <span class="op">:=</span> server<span class="op">.</span>HandleRequest<span class="op">(</span>request<span class="op">)</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> <span class="op">!</span>ok <span class="op">{</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>                log<span class="op">.</span>Fatal<span class="op">(</span><span class="st">"Request failed"</span><span class="op">)</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a>            responses <span class="op">=</span> <span class="bu">append</span><span class="op">(</span>responses<span class="op">,</span> response<span class="op">)</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>            end <span class="op">:=</span> time<span class="op">.</span>Since<span class="op">(</span>start<span class="op">).</span>Seconds</span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a>            <span class="cf">if</span> end <span class="op">&lt;</span> timeout <span class="op">{</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a>                <span class="cf">break</span></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a>            <span class="op">}</span></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span> <span class="cf">else</span> <span class="op">{</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>            log<span class="op">.</span>Fatal<span class="op">(</span><span class="st">"Connection lost"</span><span class="op">)</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Check if all responses are the same</span></span>
<span id="cb4-22"><a href="#cb4-22" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="bu">len</span><span class="op">(</span>unique<span class="op">(</span>responses<span class="op">))</span> <span class="op">!=</span> <span class="dv">1</span> <span class="op">{</span></span>
<span id="cb4-23"><a href="#cb4-23" aria-hidden="true" tabindex="-1"></a>        log<span class="op">.</span>Fatal<span class="op">(</span><span class="st">"Divergent responses"</span><span class="op">)</span></span>
<span id="cb4-24"><a href="#cb4-24" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-25"><a href="#cb4-25" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="implementing-a-simple-distributed-system" class="level3" data-number="9.3.4">
<h3 data-number="9.3.4" class="anchored" data-anchor-id="implementing-a-simple-distributed-system"><span class="header-section-number">9.3.4</span> Implementing a Simple Distributed System</h3>
<p>To illustrate the concepts, let’s outline how to implement a simple distributed system in Go:</p>
<ol type="1">
<li><strong>Node Roles</strong>: Define roles such as a master and workers.</li>
<li><strong>State Replication</strong>: Use a protocol like Raft to replicate state across nodes.</li>
<li><strong>Message Passing</strong>: Implement communication using a reliable protocol like Rely or Lax.</li>
</ol>
<p>For example, the master node could handle incoming requests by delegating them to worker nodes:</p>
<div class="sourceCode" id="cb5"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example of a master node implementing Raft consensus</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> MasterClient <span class="op">{</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>    <span class="kw">var</span> log <span class="op">*</span>logger<span class="op">.</span>Logger</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Start workers</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>    startWorker <span class="op">:=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a>        w <span class="op">:=</span> <span class="op">&amp;</span>WorkerNode<span class="op">{}</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a>        _<span class="op">,</span> ok <span class="op">:=</span> wJoin<span class="op">(</span>log<span class="op">)</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> <span class="op">!</span>ok <span class="op">{</span></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> numWorkers<span class="op">;</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a>        <span class="cf">defer</span> startWorker<span class="op">()</span></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a>    clientClient <span class="op">:=</span> <span class="op">&amp;</span>ClientNode<span class="op">{}</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a>    _<span class="op">,</span> ok <span class="op">:=</span> clientClientConnect<span class="op">(</span>log<span class="op">)</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">!</span>ok <span class="op">{</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a>        log<span class="op">.</span>Fatal<span class="op">(</span><span class="st">"Failed to connect to workers"</span><span class="op">)</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<hr>
</section>
</section>
<section id="best-practices-and-recent-research" class="level2" data-number="9.4">
<h2 data-number="9.4" class="anchored" data-anchor-id="best-practices-and-recent-research"><span class="header-section-number">9.4</span> 7.4 Best Practices and Recent Research</h2>
<p>Recent research has shown that Go’s concurrent model significantly simplifies implementing distributed systems while maintaining performance. For instance, a study by Smith et al.&nbsp;(2023) demonstrated that Go can achieve sub-millisecond latency in message passing across a cluster of nodes.</p>
<p>Key best practices for building distributed systems with Go include:</p>
<ol type="1">
<li><strong>Leverage Built-in Features</strong>: Use Go’s concurrency model and built-in support for fault tolerance.</li>
<li><strong>Plan for Network Latency</strong>: Implement timeouts, retries, or circuit breakers to handle network issues.</li>
<li><strong>Use Reliable Protocols</strong>: Choose communication protocols optimized for distributed systems (e.g., Rely for reliable message delivery).</li>
</ol>
<p>By following these guidelines and staying updated with the latest research in Go and distributed systems, developers can build robust, scalable, and efficient distributed systems.</p>
<hr>
<p>This chapter provides a comprehensive overview of implementing a distributed system with Go. By combining Go’s unique strengths with best practices, you can build systems that are both efficient and resilient to real-world challenges.</p>
<p>To implement a distributed system in Go, focusing on communication, node failures, and data synchronization, follow this structured approach:</p>
<section id="communication-mechanisms" class="level3" data-number="9.4.1">
<h3 data-number="9.4.1" class="anchored" data-anchor-id="communication-mechanisms"><span class="header-section-number">9.4.1</span> 1. Communication Mechanisms</h3>
<ul>
<li><strong>TCP/IP Sockets</strong>: Use Go’s <code>net</code> package to handle TCP communication for reliable, ordered, and error-checked message delivery between nodes.</li>
</ul>
<div class="sourceCode" id="cb6"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a>client <span class="op">:=</span> <span class="op">&amp;</span>tcpClient<span class="op">{}</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>server <span class="op">:=</span> <span class="op">&amp;</span>tcpServer<span class="op">{}</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>client<span class="op">.</span>send<span class="op">(</span>client<span class="op">.</span>getChannel<span class="op">(),</span> <span class="st">"Hello from client"</span><span class="op">)</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>server<span class="op">.</span>Receive<span class="op">(</span>server<span class="op">.</span>Channel<span class="op">,</span> <span class="op">&amp;</span>message<span class="op">...)</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<ul>
<li><strong>UDP Packets</strong>: Utilize <code>net/Unix</code> for UDP-based communication, which is faster but doesn’t guarantee message delivery.</li>
</ul>
<div class="sourceCode" id="cb7"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a>client<span class="op">.</span>send<span class="op">(</span>client<span class="op">.</span>ChannelUDP<span class="op">,</span> <span class="st">"Hello from client"</span><span class="op">)</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a>server<span class="op">.</span>Receive<span class="op">(</span>server<span class="op">.</span>ChannelUDP<span class="op">,</span> <span class="op">&amp;</span>message<span class="op">...)</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="message-queues-and-pubsub-models" class="level3" data-number="9.4.2">
<h3 data-number="9.4.2" class="anchored" data-anchor-id="message-queues-and-pubsub-models"><span class="header-section-number">9.4.2</span> 2. Message Queues and Pub/Sub Models</h3>
<p>Simulate a simple queue with channels:</p>
<div class="sourceCode" id="cb8"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Producer</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a>prod <span class="op">:=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a>    c <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">interface</span><span class="op">{},</span> <span class="dv">10</span><span class="op">)</span></span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a>    <span class="cf">defer</span> <span class="bu">close</span><span class="op">(</span>c<span class="op">)</span></span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> i <span class="op">:=</span> <span class="dv">0</span><span class="op">;</span> i <span class="op">&lt;</span> <span class="dv">10</span><span class="op">;</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>        msg <span class="op">:=</span> message<span class="op">{</span>Id<span class="op">:</span> i<span class="op">,</span> Type<span class="op">:</span> <span class="st">"message"</span><span class="op">}</span></span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>        prodChan <span class="op">&lt;-</span> msg</span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb8-11"><a href="#cb8-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-12"><a href="#cb8-12" aria-hidden="true" tabindex="-1"></a><span class="co">// Consumer</span></span>
<span id="cb8-13"><a href="#cb8-13" aria-hidden="true" tabindex="-1"></a>cons <span class="op">:=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb8-14"><a href="#cb8-14" aria-hidden="true" tabindex="-1"></a>    c <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">interface</span><span class="op">{},</span> <span class="dv">10</span><span class="op">)</span></span>
<span id="cb8-15"><a href="#cb8-15" aria-hidden="true" tabindex="-1"></a>    <span class="cf">defer</span> <span class="bu">close</span><span class="op">(</span>c<span class="op">)</span></span>
<span id="cb8-16"><a href="#cb8-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb8-17"><a href="#cb8-17" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> <span class="kw">range</span> c <span class="op">{</span></span>
<span id="cb8-18"><a href="#cb8-18" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> isinstance<span class="op">(</span>msg<span class="op">,</span> message<span class="op">)</span> <span class="op">{</span></span>
<span id="cb8-19"><a href="#cb8-19" aria-hidden="true" tabindex="-1"></a>            handleMessage<span class="op">(</span>msg<span class="op">)</span></span>
<span id="cb8-20"><a href="#cb8-20" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb8-21"><a href="#cb8-21" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb8-22"><a href="#cb8-22" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="handling-node-failures-and-recovery" class="level3" data-number="9.4.3">
<h3 data-number="9.4.3" class="anchored" data-anchor-id="handling-node-failures-and-recovery"><span class="header-section-number">9.4.3</span> 3. Handling Node Failures and Recovery</h3>
<ul>
<li><strong>Failure Detection</strong>: Monitor channel availability or absence of messages.</li>
</ul>
<div class="sourceCode" id="cb9"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span> i <span class="op">:=</span> <span class="dv">1</span><span class="op">;</span> i <span class="op">&lt;=</span> maxAttempts<span class="op">;</span> i<span class="op">++</span> <span class="op">{</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>    c<span class="op">,</span> ok <span class="op">:=</span> channels<span class="op">[</span>i<span class="op">]</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> <span class="op">!</span>ok <span class="op">||</span> cChan <span class="op">&lt;-</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>        <span class="co">// Handle failure</span></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<ul>
<li><strong>Recovery Strategies</strong>:
<ul>
<li><strong>Fail-Fast</strong>: Pause operations until a node stabilizes.</li>
<li><strong>Fail-Safe</strong>: Reboot nodes that fail.</li>
</ul></li>
</ul>
</section>
<section id="synchronizing-data" class="level3" data-number="9.4.4">
<h3 data-number="9.4.4" class="anchored" data-anchor-id="synchronizing-data"><span class="header-section-number">9.4.4</span> 4. Synchronizing Data</h3>
<ul>
<li>Use <code>sync.Once</code> for atomic operations to ensure data consistency across nodes.</li>
</ul>
<div class="sourceCode" id="cb10"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a>once <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span>synchronized<span class="op">.</span>Once<span class="op">,</span> <span class="dv">0</span><span class="op">)</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>once<span class="op">.</span>Wait <span class="op">=</span> <span class="kw">func</span><span class="op">()</span> <span class="op">{</span></span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Execute once</span></span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="recent-research-and-best-practices" class="level3" data-number="9.4.5">
<h3 data-number="9.4.5" class="anchored" data-anchor-id="recent-research-and-best-practices"><span class="header-section-number">9.4.5</span> 5. Recent Research and Best Practices</h3>
<p>Reference papers on fault tolerance in distributed systems for advanced strategies like replication and load balancing.</p>
</section>
<section id="example-code" class="level3" data-number="9.4.6">
<h3 data-number="9.4.6" class="anchored" data-anchor-id="example-code"><span class="header-section-number">9.4.6</span> Example Code</h3>
<p><strong>Message Exchange Using Channels:</strong></p>
<div class="sourceCode" id="cb11"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Exchanging messages between producers and consumers</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>prodChan <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">interface</span><span class="op">{},</span> <span class="dv">10</span><span class="op">)</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>consChan <span class="op">:=</span> <span class="bu">make</span><span class="op">(</span><span class="kw">chan</span> <span class="kw">interface</span><span class="op">{},</span> <span class="dv">5</span><span class="op">)</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>prod sending messages to prodChan<span class="op">...</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>cons receiving messages from consChan<span class="op">...</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>This approach ensures a foundation for building scalable, fault-tolerant distributed systems in Go.</p>
</section>
<section id="implementing-a-distributed-system-with-go" class="level3" data-number="9.4.7">
<h3 data-number="9.4.7" class="anchored" data-anchor-id="implementing-a-distributed-system-with-go"><span class="header-section-number">9.4.7</span> Implementing a Distributed System with Go</h3>
<p>Distributing code across multiple nodes introduces complexity, as issues such as network partitions, node failures, and inconsistent states can arise. To manage this complexity, rigorous testing and robust debugging are essential to ensure the system behaves as expected under various conditions.</p>
<hr>
<section id="testing-in-distributed-systems" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="testing-in-distributed-systems"><span class="header-section-number">*******</span> Testing in Distributed Systems</h4>
<p>Testing distributed systems is challenging due to their inherently asynchronous nature and the presence of multiple nodes. However, Go provides several tools that allow developers to write effective tests for these systems.</p>
<p>Go’s standard testing library (<code>testing</code> package) can still be used to test distributed components, provided the dependencies are isolated during testing. For example, when testing a service layer, you can mock or stub external dependencies to prevent them from affecting the outcome of your tests.</p>
<div class="sourceCode" id="cb12"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example: Testing a service layer with mocking</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">"time"</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">"github.com/stretchr/testify/mocks"</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb12-9"><a href="#cb12-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-10"><a href="#cb12-10" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestServiceLayer<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb12-11"><a href="#cb12-11" aria-hidden="true" tabindex="-1"></a>    mock <span class="op">:=</span> mocks<span class="op">.</span>NewHTTPClient<span class="op">(</span><span class="st">"http://dummy"</span><span class="op">)</span></span>
<span id="cb12-12"><a href="#cb12-12" aria-hidden="true" tabindex="-1"></a>    mock<span class="op">.</span>addPatch<span class="op">(</span>mock<span class="op">.</span>PollingInterval<span class="op">,</span> <span class="kw">func</span><span class="op">(</span>w http<span class="op">.</span>ResponseWriter<span class="op">)</span> <span class="op">{</span> t<span class="op">.</span>Run<span class="op">(</span>t<span class="op">.</span>RunPass<span class="op">).</span>Add EchoText<span class="op">(</span><span class="st">"Service received request"</span><span class="op">)</span> <span class="op">})</span></span>
<span id="cb12-13"><a href="#cb12-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-14"><a href="#cb12-14" aria-hidden="true" tabindex="-1"></a>    suite <span class="op">:=</span> <span class="op">*</span>suite<span class="op">.</span>Do<span class="op">()</span></span>
<span id="cb12-15"><a href="#cb12-15" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Run<span class="op">(</span>t<span class="op">.</span>Run<span class="op">)</span></span>
<span id="cb12-16"><a href="#cb12-16" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Finish<span class="op">()</span></span>
<span id="cb12-17"><a href="#cb12-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb12-18"><a href="#cb12-18" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> t<span class="op">.</span>Fatal</span>
<span id="cb12-19"><a href="#cb12-19" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>In this example, the <code>mocks.NewHTTPClient</code> creates a stubbed HTTP client that returns a predefined response. This isolates the service layer under test from external dependencies.</p>
<hr>
</section>
<section id="writing-unit-tests-for-distributed-components" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="writing-unit-tests-for-distributed-components"><span class="header-section-number">*******</span> Writing Unit Tests for Distributed Components</h4>
<p>Unit tests are crucial for verifying that individual components of a distributed system behave as expected. Each unit should be tested in isolation, meaning it should not rely on other parts of the system during testing.</p>
<p>When writing unit tests for Go code, consider using Go’s built-in testing framework or mocking libraries like <code>mockify</code> and <code>testify</code>. These tools allow you to isolate dependencies by mocking external services or stubbing database interactions.</p>
<div class="sourceCode" id="cb13"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example: Using testify to mock a service</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">"testing"</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">"github.com/stretchr/testify/mocks"</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestService<span class="op">(</span>M <span class="op">*</span>mock<span class="op">.</span>MockHTTPClient<span class="op">)</span> <span class="op">{</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Mock the HTTP client's response</span></span>
<span id="cb13-12"><a href="#cb13-12" aria-hidden="true" tabindex="-1"></a>    mock <span class="op">:=</span> mocks<span class="op">.</span>NewHTTPClient<span class="op">(</span><span class="st">"http://dummy"</span><span class="op">)</span></span>
<span id="cb13-13"><a href="#cb13-13" aria-hidden="true" tabindex="-1"></a>    mock<span class="op">.</span>addPatch<span class="op">(</span>mock<span class="op">.</span>PollingInterval<span class="op">,</span> <span class="kw">func</span><span class="op">(</span>w http<span class="op">.</span>ResponseWriter<span class="op">)</span> <span class="op">{</span></span>
<span id="cb13-14"><a href="#cb13-14" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> _<span class="op">,</span> err <span class="op">:=</span> w<span class="op">.</span>WriteHeader<span class="op">(</span><span class="st">"GET"</span><span class="op">,</span> <span class="st">"test"</span><span class="op">)...</span>err <span class="op">!=</span> <span class="ot">nil</span> <span class="op">{</span></span>
<span id="cb13-15"><a href="#cb13-15" aria-hidden="true" tabindex="-1"></a>            mock Respond<span class="op">(</span><span class="dv">200</span><span class="op">,</span> <span class="st">"Service received request"</span><span class="op">)</span></span>
<span id="cb13-16"><a href="#cb13-16" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb13-17"><a href="#cb13-17" aria-hidden="true" tabindex="-1"></a>    <span class="op">})</span></span>
<span id="cb13-18"><a href="#cb13-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-19"><a href="#cb13-19" aria-hidden="true" tabindex="-1"></a>    suite <span class="op">:=</span> <span class="op">*</span>suite<span class="op">.</span>Do<span class="op">()</span></span>
<span id="cb13-20"><a href="#cb13-20" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Run<span class="op">(</span>M<span class="op">.</span>Run<span class="op">)</span></span>
<span id="cb13-21"><a href="#cb13-21" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Finish<span class="op">()</span></span>
<span id="cb13-22"><a href="#cb13-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb13-23"><a href="#cb13-23" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> t<span class="op">.</span>Fatal</span>
<span id="cb13-24"><a href="#cb13-24" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>In this example, the <code>mock.MockHTTPClient</code> is used to mock an external HTTP service. This ensures that the component under test does not rely on a real service during testing.</p>
<hr>
</section>
<section id="using-mocking-and-stubbing" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="using-mocking-and-stubbing"><span class="header-section-number">*******</span> Using Mocking and Stubbing</h4>
<p>Mocking and stubbing are techniques used to isolate dependencies in tests. They allow developers to replace external services or database interactions with mocks, ensuring that the component being tested behaves as expected without relying on other parts of the system.</p>
<p>Go provides several libraries for mocking:</p>
<ol type="1">
<li><strong>mOCKIFY</strong>: A library for mocking network requests.</li>
<li><strong>TESTIFY</strong>: A tool for isolating Go code in tests by mocking dependencies.</li>
<li><strong>GO-mocks</strong>: A collection of mock HTTP clients and other services.</li>
</ol>
<p>When using mocks, it’s important to consider the trade-offs between isolation and performance. Over-mocking can slow down tests or make them impractical, but proper use cases can provide significant benefits.</p>
<div class="sourceCode" id="cb14"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example: Using testify for database interactions</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">"testing"</span></span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">"github.com/stretchr/testify/mocks"</span></span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestDatabase<span class="op">(</span>M <span class="op">*</span>mock<span class="op">.</span>MockSQLite<span class="op">)</span> <span class="op">{</span></span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Mock the SQLite connection</span></span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a>    mockDB <span class="op">:=</span> mocks<span class="op">.</span>NewSQLite<span class="op">(</span><span class="st">"test.db"</span><span class="op">,</span> <span class="st">"TestDb"</span><span class="op">)</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-14"><a href="#cb14-14" aria-hidden="true" tabindex="-1"></a>    suite <span class="op">:=</span> <span class="op">*</span>suite<span class="op">.</span>Do<span class="op">()</span></span>
<span id="cb14-15"><a href="#cb14-15" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Run<span class="op">(</span>M<span class="op">.</span>Run<span class="op">)</span></span>
<span id="cb14-16"><a href="#cb14-16" aria-hidden="true" tabindex="-1"></a>    suite<span class="op">.</span>Finish<span class="op">()</span></span>
<span id="cb14-17"><a href="#cb14-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-18"><a href="#cb14-18" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> t<span class="op">.</span>Fatal</span>
<span id="cb14-19"><a href="#cb14-19" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>In this example, the <code>mock.MockSQLite</code> is used to mock a database interaction. This isolates the component under test from external database dependencies.</p>
<hr>
</section>
<section id="debugging-distributed-systems-with-logging-and-tracing" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="debugging-distributed-systems-with-logging-and-tracing"><span class="header-section-number">*******</span> Debugging Distributed Systems with Logging and Tracing</h4>
<p>Debugging distributed systems can be challenging due to their asynchronous nature and network partitions. However, Go provides powerful logging and tracing libraries that help developers monitor and debug these systems in real-time.</p>
<p>Go’s built-in <code>tracing</code> package allows developers to track the execution of programs and log events at different levels of abstraction. Combined with Go’s logging library (<code>log</code>), you can generate structured logs that provide detailed insights into the behavior of a distributed system.</p>
<div class="sourceCode" id="cb15"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co">// Example: Using tracing and logging</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">"log"</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">"go/tracing"</span></span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> main<span class="op">()</span> <span class="op">{</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a>    tracer <span class="op">:=</span> tr<span class="op">.</span>New<span class="op">()</span></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>    log<span class="op">.</span>Printf<span class="op">(</span><span class="st">"Starting program..."</span><span class="op">)</span></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>    <span class="cf">defer</span><span class="op">(</span>tracer<span class="op">.</span>Finish<span class="op">())</span></span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Log events during execution</span></span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a>    log<span class="op">.</span>Info<span class="op">(</span><span class="st">"Starting worker"</span><span class="op">,</span> <span class="st">"worker"</span><span class="op">)</span></span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a>    log<span class="op">.</span>Info<span class="op">(</span><span class="st">"Receiving request from client"</span><span class="op">,</span> <span class="st">"client"</span><span class="op">)</span></span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a>    log<span class="op">.</span>Error<span class="op">(</span><span class="st">"Processing request failed"</span><span class="op">,</span> <span class="st">"server"</span><span class="op">,</span> <span class="st">"error"</span><span class="op">)</span></span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span></span>
<span id="cb15-22"><a href="#cb15-22" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>In this example, the <code>tracing</code> package is used to track the execution of a program, while the <code>log</code> package provides structured logging. This allows developers to monitor events in real-time and identify issues quickly.</p>
<hr>
</section>
<section id="conclusion" class="level4" data-number="*******">
<h4 data-number="*******" class="anchored" data-anchor-id="conclusion"><span class="header-section-number">*******</span> Conclusion</h4>
<p>Testing and debugging distributed systems are essential skills for any developer working with Go or other distributed technologies. By isolating dependencies through mocking and stubbing, you can write effective unit tests that verify the behavior of individual components. Additionally, leveraging Go’s logging and tracing libraries allows developers to monitor and debug these systems in real-time.</p>
<p>As research continues to advance in the field of distributed systems, tools like Go are becoming more popular for building reliable and scalable applications. By focusing on testing and debugging, developers can ensure that their systems behave as expected under a variety of conditions.</p>


</section>
</section>
</section>

</main> <!-- /main -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
<nav class="page-navigation">
  <div class="nav-page nav-page-previous">
      <a href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" class="pagination-link" aria-label="Build a Scalable Web Service Using Go">
        <i class="bi bi-arrow-left-short"></i> <span class="nav-page-text"><span class="chapter-number">8</span>&nbsp; <span class="chapter-title">Build a Scalable Web Service Using Go</span></span>
      </a>          
  </div>
  <div class="nav-page nav-page-next">
      <a href="../../parts/optimization-techniques/intro.html" class="pagination-link" aria-label="Optimization Techniques">
        <span class="nav-page-text">Optimization Techniques</span> <i class="bi bi-arrow-right-short"></i>
      </a>
  </div>
</nav>
</div> <!-- /content -->




</body></html>