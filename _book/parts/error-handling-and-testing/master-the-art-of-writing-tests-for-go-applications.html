<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">


<title>13&nbsp; Writing Tests for Go Applications – The Complete Guide to GoLang</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="../../site_libs/quarto-nav/quarto-nav.js"></script>
<script src="../../site_libs/quarto-nav/headroom.min.js"></script>
<script src="../../site_libs/clipboard/clipboard.min.js"></script>
<script src="../../site_libs/quarto-search/autocomplete.umd.js"></script>
<script src="../../site_libs/quarto-search/fuse.min.js"></script>
<script src="../../site_libs/quarto-search/quarto-search.js"></script>
<meta name="quarto:offset" content="../../">
<link href="../../parts/advanced-topics/intro.html" rel="next">
<link href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" rel="prev">
<script src="../../site_libs/quarto-html/quarto.js"></script>
<script src="../../site_libs/quarto-html/popper.min.js"></script>
<script src="../../site_libs/quarto-html/tippy.umd.min.js"></script>
<script src="../../site_libs/quarto-html/anchor.min.js"></script>
<link href="../../site_libs/quarto-html/tippy.css" rel="stylesheet">
<link href="../../site_libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="../../site_libs/bootstrap/bootstrap.min.js"></script>
<link href="../../site_libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="../../site_libs/bootstrap/bootstrap-a2a08d6480f1a07d2e84f5b3bded3372.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">
<script id="quarto-search-options" type="application/json">{
  "location": "sidebar",
  "copy-button": false,
  "collapse-after": 3,
  "panel-placement": "start",
  "type": "textbox",
  "limit": 50,
  "keyboard-shortcut": [
    "f",
    "/",
    "s"
  ],
  "show-item-context": false,
  "language": {
    "search-no-results-text": "No results",
    "search-matching-documents-text": "matching documents",
    "search-copy-link-title": "Copy link to search",
    "search-hide-matches-text": "Hide additional matches",
    "search-more-match-text": "more match in this document",
    "search-more-matches-text": "more matches in this document",
    "search-clear-button-title": "Clear",
    "search-text-placeholder": "",
    "search-detached-cancel-button-title": "Cancel",
    "search-submit-button-title": "Submit",
    "search-label": "Search"
  }
}</script>


</head>

<body class="nav-sidebar floating">

<div id="quarto-search-results"></div>
  <header id="quarto-header" class="headroom fixed-top">
  <nav class="quarto-secondary-nav">
    <div class="container-fluid d-flex">
      <button type="button" class="quarto-btn-toggle btn" data-bs-toggle="collapse" role="button" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">
        <i class="bi bi-layout-text-sidebar-reverse"></i>
      </button>
        <nav class="quarto-page-breadcrumbs" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/error-handling-and-testing/intro.html">Error Handling and Testing</a></li><li class="breadcrumb-item"><a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></a></li></ol></nav>
        <a class="flex-grow-1" role="navigation" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item" aria-controls="quarto-sidebar" aria-expanded="false" aria-label="Toggle sidebar navigation" onclick="if (window.quartoToggleHeadroom) { window.quartoToggleHeadroom(); }">      
        </a>
      <button type="button" class="btn quarto-search-button" aria-label="Search" onclick="window.quartoOpenSearch();">
        <i class="bi bi-search"></i>
      </button>
    </div>
  </nav>
</header>
<!-- content -->
<div id="quarto-content" class="quarto-container page-columns page-rows-contents page-layout-article">
<!-- sidebar -->
  <nav id="quarto-sidebar" class="sidebar collapse collapse-horizontal quarto-sidebar-collapse-item sidebar-navigation floating overflow-auto">
    <div class="pt-lg-2 mt-2 text-left sidebar-header">
    <div class="sidebar-title mb-0 py-0">
      <a href="../../">The Complete Guide to GoLang</a> 
    </div>
      </div>
        <div class="mt-2 flex-shrink-0 align-items-center">
        <div class="sidebar-search">
        <div id="quarto-search" class="" title="Search"></div>
        </div>
        </div>
    <div class="sidebar-menu-container"> 
    <ul class="list-unstyled mt-1">
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../index.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Preface</span></a>
  </div>
</li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/go-fundamentals/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Go Fundamentals</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-1" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-1" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/dive-deep-into-go-syntax-variables-types-and-control-structures.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">1</span>&nbsp; <span class="chapter-title">Introduction to Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/master-arrays-slices-maps-and-struct-types-for-efficient-data-manipulation.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">2</span>&nbsp; <span class="chapter-title">Arrays and Slices</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/go-fundamentals/explore-interfaces-error-handling-and-package-management.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">3</span>&nbsp; <span class="chapter-title">Explore Interfaces, Error Handling, and Package Management</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/concurrent-programming/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Concurrent Programming</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-2" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-2" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/unravel-the-power-of-gos-concurrency-model-with-goroutines-and-channels.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">4</span>&nbsp; <span class="chapter-title">Unravel the Power of Go’s Concurrency Model with Goroutines and Channels</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/concurrent-programming/master-the-art-of-parallelism-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">5</span>&nbsp; <span class="chapter-title">Overview of Parallelism and Concurrency in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/complex-data-structures/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Complex Data Structures</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-3" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-3" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/explore-advanced-data-structures-such-as-trees-and-graphs.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">6</span>&nbsp; <span class="chapter-title">5. Trees</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/complex-data-structures/master-the-art-of-sorting-and-searching-complex-data.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">7</span>&nbsp; <span class="chapter-title">Mastering Sorting and Searching Complex Data in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/real-world-applications/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Real-World Applications</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-4" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-4" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/build-a-scalable-web-service-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">8</span>&nbsp; <span class="chapter-title">Build a Scalable Web Service Using Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/real-world-applications/implement-a-distributed-system-with-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">9</span>&nbsp; <span class="chapter-title">Chapter 7: Implement a Distributed System with Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/optimization-techniques/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Optimization Techniques</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-5" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-5" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/learn-best-practices-for-writing-efficient-go-code.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">10</span>&nbsp; <span class="chapter-title">Writing Efficient Go Code</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/optimization-techniques/master-the-art-of-profiling-and-optimizing-go-applications.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">11</span>&nbsp; <span class="chapter-title">Master the Art of Profiling and Optimizing Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/error-handling-and-testing/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Error Handling and Testing</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-6" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-6" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">12</span>&nbsp; <span class="chapter-title">Overview of Error Handling in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html" class="sidebar-item-text sidebar-link active">
 <span class="menu-text"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/advanced-topics/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Advanced Topics</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-7" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-7" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/explore-advanced-topics-in-go-such-as-coroutines-and-fibers.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">14</span>&nbsp; <span class="chapter-title">What are Coroutines?</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/advanced-topics/master-the-art-of-writing-concurrent-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">15</span>&nbsp; <span class="chapter-title">Introduction to Concurrent Programming</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/case-studies-and-best-practices/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Case Studies and Best Practices</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-8" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-8" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/learn-from-real-world-case-studies-of-using-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">16</span>&nbsp; <span class="chapter-title">Case Study: Building a Scalable Backend with Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/case-studies-and-best-practices/master-the-art-of-writing-maintainable-and-scalable-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">17</span>&nbsp; <span class="chapter-title">Introduction to Writing Maintainable and Scalable Code in Go</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item sidebar-item-section">
      <div class="sidebar-item-container"> 
            <a href="../../parts/future-proofing-your-go-code/intro.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text">Future-Proofing Your Go Code</span></a>
          <a class="sidebar-item-toggle text-start" data-bs-toggle="collapse" data-bs-target="#quarto-sidebar-section-9" role="navigation" aria-expanded="true" aria-label="Toggle section">
            <i class="bi bi-chevron-right ms-2"></i>
          </a> 
      </div>
      <ul id="quarto-sidebar-section-9" class="collapse list-unstyled sidebar-section depth1 show">  
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/learn-how-to-write-future-proof-code-in-go.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">18</span>&nbsp; <span class="chapter-title">Learn How to Write Future-Proof Code in Go</span></span></a>
  </div>
</li>
          <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../parts/future-proofing-your-go-code/master-the-art-of-adapting-to-changing-requirements-and-technology.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">19</span>&nbsp; <span class="chapter-title">Mastering Adaptability</span></span></a>
  </div>
</li>
      </ul>
  </li>
        <li class="sidebar-item">
  <div class="sidebar-item-container"> 
  <a href="../../summary.html" class="sidebar-item-text sidebar-link">
 <span class="menu-text"><span class="chapter-number">20</span>&nbsp; <span class="chapter-title">Summary</span></span></a>
  </div>
</li>
    </ul>
    </div>
</nav>
<div id="quarto-sidebar-glass" class="quarto-sidebar-collapse-item" data-bs-toggle="collapse" data-bs-target=".quarto-sidebar-collapse-item"></div>
<!-- margin-sidebar -->
    <div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
        <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Table of contents</h2>
   
  <ul>
  <li><a href="#why-write-tests-for-your-go-applications" id="toc-why-write-tests-for-your-go-applications" class="nav-link active" data-scroll-target="#why-write-tests-for-your-go-applications"><span class="header-section-number">13.1</span> Why Write Tests for Your Go Applications?</a></li>
  <li><a href="#best-practices-for-writing-effective-tests" id="toc-best-practices-for-writing-effective-tests" class="nav-link" data-scroll-target="#best-practices-for-writing-effective-tests"><span class="header-section-number">13.2</span> Best Practices for Writing Effective Tests</a></li>
  <li><a href="#test-frameworks-and-tools" id="toc-test-frameworks-and-tools" class="nav-link" data-scroll-target="#test-frameworks-and-tools"><span class="header-section-number">13.3</span> Test Frameworks and Tools</a>
  <ul class="collapse">
  <li><a href="#overview-of-popular-test-frameworks-in-go" id="toc-overview-of-popular-test-frameworks-in-go" class="nav-link" data-scroll-target="#overview-of-popular-test-frameworks-in-go"><span class="header-section-number">13.3.1</span> Overview of Popular Test Frameworks in Go</a></li>
  <li><a href="#using-gos-built-in-testing-library-testing.t" id="toc-using-gos-built-in-testing-library-testing.t" class="nav-link" data-scroll-target="#using-gos-built-in-testing-library-testing.t"><span class="header-section-number">13.3.2</span> Using Go’s Built-in Testing Library: Testing.T</a></li>
  <li><a href="#third-party-test-frameworks-ginkgo-gomega-and-more" id="toc-third-party-test-frameworks-ginkgo-gomega-and-more" class="nav-link" data-scroll-target="#third-party-test-frameworks-ginkgo-gomega-and-more"><span class="header-section-number">13.3.3</span> Third-Party Test Frameworks: Ginkgo, Gomega, and More</a></li>
  </ul></li>
  <li><a href="#test-case-structure-and-organization" id="toc-test-case-structure-and-organization" class="nav-link" data-scroll-target="#test-case-structure-and-organization"><span class="header-section-number">13.4</span> Test Case Structure and Organization</a></li>
  <li><a href="#writing-effective-test-cases-tips-and-tricks" id="toc-writing-effective-test-cases-tips-and-tricks" class="nav-link" data-scroll-target="#writing-effective-test-cases-tips-and-tricks"><span class="header-section-number">13.5</span> Writing Effective Test Cases: Tips and Tricks</a></li>
  <li><a href="#using-tags-and-labels-for-better-test-management" id="toc-using-tags-and-labels-for-better-test-management" class="nav-link" data-scroll-target="#using-tags-and-labels-for-better-test-management"><span class="header-section-number">13.6</span> Using Tags and Labels for Better Test Management</a></li>
  <li><a href="#conclusion" id="toc-conclusion" class="nav-link" data-scroll-target="#conclusion"><span class="header-section-number">13.7</span> Conclusion</a>
  <ul class="collapse">
  <li><a href="#chapter-master-the-art-of-writing-tests-for-go-applications" id="toc-chapter-master-the-art-of-writing-tests-for-go-applications" class="nav-link" data-scroll-target="#chapter-master-the-art-of-writing-tests-for-go-applications"><span class="header-section-number">13.7.1</span> Chapter: Master the Art of Writing Tests for Go Applications</a></li>
  <li><a href="#conclusion-1" id="toc-conclusion-1" class="nav-link" data-scroll-target="#conclusion-1"><span class="header-section-number">13.7.2</span> Conclusion</a></li>
  </ul></li>
  </ul>
</nav>
    </div>
<!-- main -->
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default"><nav class="quarto-page-breadcrumbs quarto-title-breadcrumbs d-none d-lg-block" aria-label="breadcrumb"><ol class="breadcrumb"><li class="breadcrumb-item"><a href="../../parts/error-handling-and-testing/intro.html">Error Handling and Testing</a></li><li class="breadcrumb-item"><a href="../../parts/error-handling-and-testing/master-the-art-of-writing-tests-for-go-applications.html"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></a></li></ol></nav>
<div class="quarto-title">
<h1 class="title"><span class="chapter-number">13</span>&nbsp; <span class="chapter-title">Writing Tests for Go Applications</span></h1>
</div>



<div class="quarto-title-meta">

    
  
    
  </div>
  


</header>


<p>Testing is an essential part of software development, ensuring that your application behaves as expected under various conditions. In Go, writing tests not only verifies functionality but also helps catch bugs early, improves maintainability, and supports a robust codebase. As Go applications grow in complexity, so does the importance of comprehensive testing strategies.</p>
<p>This section dives into the fundamentals of writing effective tests for Go applications, covering best practices, test frameworks, and organizing your test suite. By the end, you’ll have a solid foundation to start writing reliable and maintainable tests for your own projects.</p>
<section id="why-write-tests-for-your-go-applications" class="level2" data-number="13.1">
<h2 data-number="13.1" class="anchored" data-anchor-id="why-write-tests-for-your-go-applications"><span class="header-section-number">13.1</span> Why Write Tests for Your Go Applications?</h2>
<p>Writing tests serves multiple purposes in the development lifecycle:</p>
<ol type="1">
<li><strong>Verification of Functionality</strong>: Tests ensure that individual components or features behave as intended.</li>
<li><strong>Early Bug Detection</strong>: By testing early, you can identify and fix issues before they become costly to resolve later.</li>
<li><strong>Improved Maintainability</strong>: Well-structured tests make your codebase easier to understand and maintain by providing clear expectations for each feature.</li>
<li><strong>Performance Testing</strong>: Go’s performance is often a critical factor, with tests helping to identify bottlenecks or regressions introduced during development.</li>
<li><strong>Security Assurance</strong>: In production environments, testing helps identify vulnerabilities that could be exploited later.</li>
</ol>
<p>In short, writing tests is not just about passing automated checks—it’s about building confidence in your application’s reliability and quality.</p>
</section>
<section id="best-practices-for-writing-effective-tests" class="level2" data-number="13.2">
<h2 data-number="13.2" class="anchored" data-anchor-id="best-practices-for-writing-effective-tests"><span class="header-section-number">13.2</span> Best Practices for Writing Effective Tests</h2>
<p>Writing effective tests requires a systematic approach. Here are some best practices to keep in mind:</p>
<ol type="1">
<li><strong>Start with Unit Tests</strong>: Begin by testing individual functions or methods before integrating them into larger components.</li>
<li><strong>Cover All Paths</strong>: Ensure that both the code under test and its dependencies (like external APIs, databases, or configuration files) are thoroughly tested across all possible paths.</li>
<li><strong>Use Mocks for External Dependencies</strong>: If your application relies on external services, mocks allow you to isolate your code from the real world during testing.</li>
<li><strong>Leverage Go’s Built-in Testing Library</strong>: The <code>testing</code> package in Go provides a straightforward way to write unit tests and integrate them into your workflow using tools like <code>go test</code>.</li>
<li><strong>Use Third-Party Frameworks When Appropriate</strong>: Tools like Ginkgo, Gomega, or Testify can simplify testing by providing ready-to-use fixtures and reducing boilerplate code.</li>
<li><strong>Maintain a Good Test-to-Code Ratio</strong>: Avoid writing tests that duplicate the functionality of your code—tests should provide additional value beyond what’s already written.</li>
</ol>
<p>By following these best practices, you’ll create tests that are not only effective but also maintainable over time.</p>
</section>
<section id="test-frameworks-and-tools" class="level2" data-number="13.3">
<h2 data-number="13.3" class="anchored" data-anchor-id="test-frameworks-and-tools"><span class="header-section-number">13.3</span> Test Frameworks and Tools</h2>
<section id="overview-of-popular-test-frameworks-in-go" class="level3" data-number="13.3.1">
<h3 data-number="13.3.1" class="anchored" data-anchor-id="overview-of-popular-test-frameworks-in-go"><span class="header-section-number">13.3.1</span> Overview of Popular Test Frameworks in Go</h3>
<p>Go has a rich ecosystem of testing frameworks, each with its own strengths:</p>
<ol type="1">
<li><strong>Testing Library (gonum.org)</strong>
<ul>
<li>The <code>testing</code> package is part of the standard library and provides basic test suite creation.</li>
</ul></li>
<li><strong>Ginkgo</strong>
<ul>
<li>A modern, actively maintained testing framework that supports mocking dependencies and writing clean tests.</li>
</ul></li>
<li><strong>Gomega</strong>
<ul>
<li>Another popular choice for functional testing, Gomega emphasizes readability and maintainability.</li>
</ul></li>
</ol>
</section>
<section id="using-gos-built-in-testing-library-testing.t" class="level3" data-number="13.3.2">
<h3 data-number="13.3.2" class="anchored" data-anchor-id="using-gos-built-in-testing-library-testing.t"><span class="header-section-number">13.3.2</span> Using Go’s Built-in Testing Library: Testing.T</h3>
<p>Go’s standard library includes <code>testing.T</code>, which is straightforward to use but less feature-rich compared to third-party frameworks like Ginkgo or Gomega.</p>
<p><strong>Example Code Using <code>Testing.T</code>:</strong></p>
<div class="sourceCode" id="cb1"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">"testing"</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestMyFunction<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>    tests <span class="op">:=</span> <span class="op">[]</span><span class="kw">struct</span> <span class="op">{</span></span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>        name     <span class="dt">string</span></span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>        want     <span class="kw">interface</span><span class="op">{}</span></span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>        wantVal  <span class="kw">interface</span><span class="op">{}</span></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>    <span class="op">}{</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>        <span class="op">{</span></span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>            name<span class="op">:</span> <span class="st">"my function returns the correct value"</span><span class="op">,</span></span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>            want<span class="op">:</span> <span class="kw">func</span><span class="op">()</span> <span class="kw">interface</span><span class="op">{}</span> <span class="op">{</span> <span class="cf">return</span> <span class="st">"hello world"</span> <span class="op">},</span></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>            wantVal<span class="op">:</span> <span class="st">"hello world"</span><span class="op">,</span></span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>        <span class="op">},</span></span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>    <span class="cf">for</span> _<span class="op">,</span> tt <span class="op">:=</span> <span class="kw">range</span> tests <span class="op">{</span></span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>        <span class="cf">if</span> tt<span class="op">.</span>name <span class="op">{</span></span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>            t<span class="op">.</span>Run<span class="op">(</span>tt<span class="op">.</span>name<span class="op">,</span> <span class="kw">func</span><span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>                <span class="cf">if</span> <span class="op">!</span>tt<span class="op">.</span>want<span class="op">(</span>tt<span class="op">.</span>f<span class="op">())</span> <span class="op">{</span></span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a>                    t<span class="op">.</span>Errorf<span class="op">(</span><span class="st">"returned %v instead of %v"</span><span class="op">,</span> tt<span class="op">.</span>wantVal<span class="op">,</span> tt<span class="op">.</span>want<span class="op">)</span></span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a>                <span class="op">}</span></span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a>            <span class="op">})</span></span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a>        <span class="op">}</span></span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb1-29"><a href="#cb1-29" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-30"><a href="#cb1-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-31"><a href="#cb1-31" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> Want<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="kw">interface</span><span class="op">{}</span> <span class="op">{</span></span>
<span id="cb1-32"><a href="#cb1-32" aria-hidden="true" tabindex="-1"></a>    <span class="co">// This function is called by the test framework to get each test's expected value</span></span>
<span id="cb1-33"><a href="#cb1-33" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb1-34"><a href="#cb1-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-35"><a href="#cb1-35" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> WantVal<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="kw">interface</span><span class="op">{}</span> <span class="op">{</span></span>
<span id="cb1-36"><a href="#cb1-36" aria-hidden="true" tabindex="-1"></a>    <span class="co">// This function returns the expected value for each test case</span></span>
<span id="cb1-37"><a href="#cb1-37" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="st">"hello world"</span></span>
<span id="cb1-38"><a href="#cb1-38" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="third-party-test-frameworks-ginkgo-gomega-and-more" class="level3" data-number="13.3.3">
<h3 data-number="13.3.3" class="anchored" data-anchor-id="third-party-test-frameworks-ginkgo-gomega-and-more"><span class="header-section-number">13.3.3</span> Third-Party Test Frameworks: Ginkgo, Gomega, and More</h3>
<p>Third-party frameworks like Ginkgo and Gomega offer more advanced features such as mocking dependencies, writing cleaner test cases, and better documentation.</p>
<p><strong>Example Code Using Ginkgo:</strong></p>
<div class="sourceCode" id="cb2"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">"ginkgo"</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    <span class="st">"testing"</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestMyFunction<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>    g <span class="op">:=</span> ginkgo<span class="op">.</span>New<span class="op">()</span></span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    tests <span class="op">:=</span> g<span class="op">.</span>NewGroup<span class="op">(</span><span class="st">"Test Cases"</span><span class="op">)</span></span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>    </span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>    tests<span class="op">.</span>Add<span class="op">(</span>g New <span class="st">"Basic Functionality"</span><span class="op">,</span> <span class="kw">func</span><span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a>        assert<span class="op">.</span>Equal<span class="op">(</span>t<span class="op">,</span> <span class="st">"hello world"</span><span class="op">,</span> MyFunction<span class="op">())</span></span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>    <span class="op">})</span></span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb2-17"><a href="#cb2-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-18"><a href="#cb2-18" aria-hidden="true" tabindex="-1"></a>suite<span class="op">,</span> _ <span class="op">:=</span> g<span class="op">.</span>Run<span class="op">(</span>t<span class="op">)</span></span>
<span id="cb2-19"><a href="#cb2-19" aria-hidden="true" tabindex="-1"></a>suite<span class="op">.</span>RunAll<span class="op">()</span></span>
<span id="cb2-20"><a href="#cb2-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-21"><a href="#cb2-21" aria-hidden="true" tabindex="-1"></a>ginkgo<span class="op">.</span>Shutdown<span class="op">()</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>Ginkgo simplifies test suite management and provides predefined fixtures for common data types.</p>
</section>
</section>
<section id="test-case-structure-and-organization" class="level2" data-number="13.4">
<h2 data-number="13.4" class="anchored" data-anchor-id="test-case-structure-and-organization"><span class="header-section-number">13.4</span> Test Case Structure and Organization</h2>
<p>Organizing your tests is as important as writing them. Here’s how to structure your test suite:</p>
<ol type="1">
<li><strong>Test Suites</strong>: Group related test cases into a single suite using packages or directory structures.</li>
<li><strong>Tagging and Filtering</strong>: Use tags in Go files to filter test cases based on priority, coverage goals, or other criteria.</li>
</ol>
<p><strong>Example Tagged Test Case:</strong></p>
<div class="sourceCode" id="cb3"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="co">// Filenames with the tag "high_priority" will be included in the test suite</span></span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a><span class="co">// if the environment variable GO_TEST SUITES includes this file.</span></span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> Tags<span class="op">()</span> <span class="dt">string</span> <span class="op">{</span></span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> os<span class="op">.</span>Mac<span class="op">()</span> <span class="op">{</span></span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span> <span class="st">"macOS"</span></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> <span class="st">"linux"</span></span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestMyFunction<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb3-13"><a href="#cb3-13" aria-hidden="true" tabindex="-1"></a>    assert<span class="op">.</span>Equal<span class="op">(</span>t<span class="op">,</span> <span class="dv">42</span><span class="op">,</span> MyCounter<span class="op">())</span></span>
<span id="cb3-14"><a href="#cb3-14" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="writing-effective-test-cases-tips-and-tricks" class="level2" data-number="13.5">
<h2 data-number="13.5" class="anchored" data-anchor-id="writing-effective-test-cases-tips-and-tricks"><span class="header-section-number">13.5</span> Writing Effective Test Cases: Tips and Tricks</h2>
<ol type="1">
<li><strong>Start with a Clear Purpose</strong>: Each test case should have a single responsibility.</li>
<li><strong>Use Mocks for External Dependencies</strong>: This isolates your code from external factors during testing.</li>
<li><strong>Handle Stateful Applications</strong>: Use <code>teardown</code> and <code>setup</code> functions to reset the application state before each test.</li>
<li><strong>Mock Dependencies</strong>: If you’re testing an API call, mock the service to return a predefined response.</li>
<li><strong>Document Your Tests</strong>: Include comments or documentation within your test cases to explain their purpose.</li>
</ol>
<p><strong>Example Test Case with Setup/Teardown:</strong></p>
<div class="sourceCode" id="cb4"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="kw">package</span> main</span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a><span class="kw">import</span> <span class="op">(</span></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">"testing"</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a><span class="op">)</span></span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> Setup<span class="op">()</span> <span class="op">{</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Reset application state before each test</span></span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-11"><a href="#cb4-11" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> TearDown<span class="op">()</span> <span class="op">{</span></span>
<span id="cb4-12"><a href="#cb4-12" aria-hidden="true" tabindex="-1"></a>    <span class="co">// Cleanup any resources after the test</span></span>
<span id="cb4-13"><a href="#cb4-13" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb4-14"><a href="#cb4-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-15"><a href="#cb4-15" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestGetUser<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb4-16"><a href="#cb4-16" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> _<span class="op">,</span> err <span class="op">:=</span> t<span class="op">.</span>Setup<span class="op">();</span> err <span class="op">!=</span> <span class="ot">nil</span><span class="op">;</span> <span class="op">{</span></span>
<span id="cb4-17"><a href="#cb4-17" aria-hidden="true" tabindex="-1"></a>        <span class="cf">return</span></span>
<span id="cb4-18"><a href="#cb4-18" aria-hidden="true" tabindex="-1"></a>    <span class="op">}</span></span>
<span id="cb4-19"><a href="#cb4-19" aria-hidden="true" tabindex="-1"></a>    assert<span class="op">.</span>NoError<span class="op">(</span>t<span class="op">,</span> <span class="st">"GET /users"</span><span class="op">)</span></span>
<span id="cb4-20"><a href="#cb4-20" aria-hidden="true" tabindex="-1"></a>    assert<span class="op">.</span>EqualJSON<span class="op">(</span>t<span class="op">,</span> <span class="st">"user details"</span><span class="op">,</span> t biopsy<span class="op">())</span></span>
<span id="cb4-21"><a href="#cb4-21" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="using-tags-and-labels-for-better-test-management" class="level2" data-number="13.6">
<h2 data-number="13.6" class="anchored" data-anchor-id="using-tags-and-labels-for-better-test-management"><span class="header-section-number">13.6</span> Using Tags and Labels for Better Test Management</h2>
<p>Tags allow you to categorize test cases based on their purpose or priority. This makes it easier to run specific subsets of your tests.</p>
<p><strong>Example Tagged Function:</strong></p>
<div class="sourceCode" id="cb5"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> <span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> Tags<span class="op">()</span> <span class="dt">string</span> <span class="op">{</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> t wants to be prioritized as high<span class="op">,</span> add a tag like <span class="st">"high_priority"</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> TestMyFunction<span class="op">(</span>t <span class="op">*</span>testing<span class="op">.</span>T<span class="op">)</span> <span class="op">{</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a>    <span class="co">// ...</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</section>
<section id="conclusion" class="level2" data-number="13.7">
<h2 data-number="13.7" class="anchored" data-anchor-id="conclusion"><span class="header-section-number">13.7</span> Conclusion</h2>
<p>Writing effective tests is crucial for maintaining the quality of your Go applications. By following best practices, using appropriate frameworks, and organizing your test suite effectively, you can ensure that your application is thoroughly tested and reliable.</p>
<p>Incorporate these tips into your workflow and gradually adopt more advanced testing frameworks as your project grows. Remember, testing should be an integral part of your development process, not just a one-time activity before deployment.</p>
<section id="chapter-master-the-art-of-writing-tests-for-go-applications" class="level3" data-number="13.7.1">
<h3 data-number="13.7.1" class="anchored" data-anchor-id="chapter-master-the-art-of-writing-tests-for-go-applications"><span class="header-section-number">13.7.1</span> Chapter: Master the Art of Writing Tests for Go Applications</h3>
<section id="testing-data-and-mocks" class="level4" data-number="********">
<h4 data-number="********" class="anchored" data-anchor-id="testing-data-and-mocks"><span class="header-section-number">********</span> Testing Data and Mocks</h4>
<section id="understanding-the-role-of-data-in-writing-good-tests" class="level5" data-number="********.1">
<h5 data-number="********.1" class="anchored" data-anchor-id="understanding-the-role-of-data-in-writing-good-tests"><span class="header-section-number">********.1</span> Understanding the Role of Data in Writing Good Tests</h5>
<p>Testing is a cornerstone of software development, ensuring that your application behaves as expected under various scenarios. In Go, writing effective tests often involves creating test data—specifically designed inputs, configurations, or states—that allow you to validate your code thoroughly. Test data can come from multiple sources: predefined datasets, mocking external dependencies, or dynamically generating values based on certain conditions.</p>
<p>The importance of test data lies in its ability to cover edge cases and boundary conditions that might not be evident during normal execution. For example, testing with extreme values (e.g., very large integers, empty strings, or null pointers) can reveal potential bugs or unexpected behavior in your code. Additionally, using mock objects allows you to simulate interactions between components of your application without relying on external services or databases.</p>
<p>Writing good test data requires careful planning and attention to detail. It is often referred to as “test coverage” because it ensures that different paths through your code are exercised during testing. To write effective test data:</p>
<ol type="1">
<li><strong>Identify Test Scenarios</strong>: Determine all possible execution paths in your application.</li>
<li><strong>Select Representative Inputs</strong>: Choose inputs that cover normal cases, edge cases, and error conditions.</li>
<li><strong>Use Structured Formats</strong>: Store test data in a structured format (e.g., JSON or YAML) for readability and reusability.</li>
<li><strong>Leverage Tools</strong>: Use tools like Go’s <code>testing</code> library or mocking frameworks to automate the loading of test data.</li>
</ol>
</section>
<section id="working-with-mocks-what-are-they-and-how-to-use-them" class="level5" data-number="********.2">
<h5 data-number="********.2" class="anchored" data-anchor-id="working-with-mocks-what-are-they-and-how-to-use-them"><span class="header-section-number">********.2</span> Working with Mocks: What Are They and How to Use Them</h5>
<p>Mock objects are placeholders that mimic the behavior of real components in your application. They allow you to isolate specific parts of your code for testing, ensuring that they behave correctly without being influenced by external factors like other modules or services.</p>
<p>In Go, mocks can be implemented using libraries such as <code>mock</code> and <code>testing</code>. The <code>mock</code> package provides decorators like <code>Mock</code>, <code>Kill</code>, and <code>Spy</code> that allow you to wrap functions and control their execution during tests. For example:</p>
<div class="sourceCode" id="cb6"><pre class="sourceCode go code-with-copy"><code class="sourceCode go"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="kw">func</span> MyFunc<span class="op">(</span>a <span class="op">*</span>Mock<span class="op">)</span> <span class="dt">int</span> <span class="op">{</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>    <span class="cf">return</span> a<span class="op">.(*</span><span class="kw">func</span><span class="op">)(</span>io<span class="op">/</span>ioutil<span class="op">.</span>ReadFile<span class="op">(</span><span class="st">"path/to/file"</span><span class="op">))</span></span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="op">}</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>Using mocks effectively requires following best practices, such as:</p>
<ol type="1">
<li><strong>Injecting Mocks</strong>: Inject mock objects into your test code to replace dependencies.</li>
<li><strong>Spying on Methods</strong>: Use <code>Spy</code> decorators to observe or modify method calls during testing.</li>
<li><strong>Managing State</strong>: Ensure that mocks maintain the correct state throughout their lifecycle.</li>
</ol>
</section>
<section id="best-practices-for-creating-effective-mock-objects" class="level5" data-number="********.3">
<h5 data-number="********.3" class="anchored" data-anchor-id="best-practices-for-creating-effective-mock-objects"><span class="header-section-number">********.3</span> Best Practices for Creating Effective Mock Objects</h5>
<p>Creating effective mock objects involves balancing flexibility and specificity:</p>
<ol type="1">
<li><strong>Mock Realistic Dependencies</strong>: Replace external dependencies (e.g., APIs, services) with mocks to isolate your code under test.</li>
<li><strong>Spy Instead of Killing</strong>: Use <code>Spy</code> instead of <code>Kill</code> to observe method calls without stopping the test.</li>
<li><strong>Leverage Mocks for Configuration</strong>: Use mocks to test how your application handles different configurations or scenarios.</li>
</ol>
<p>By mastering these techniques, you can significantly improve the reliability and robustness of your Go applications through effective testing.</p>
<hr>
</section>
</section>
<section id="test-coverage-and-analysis" class="level4" data-number="********">
<h4 data-number="********" class="anchored" data-anchor-id="test-coverage-and-analysis"><span class="header-section-number">********</span> Test Coverage and Analysis</h4>
<section id="what-is-test-coverage-and-why-should-you-care" class="level5" data-number="********.1">
<h5 data-number="********.1" class="anchored" data-anchor-id="what-is-test-coverage-and-why-should-you-care"><span class="header-section-number">********.1</span> What is Test Coverage, and Why Should You Care?</h5>
<p>Test coverage refers to the measure of code execution during automated tests. It quantifies how much of your source code has been tested for functionality. High test coverage ensures that critical parts of your code are thoroughly tested, reducing the risk of regressions and improving maintainability.</p>
<p>In Go, test coverage is typically measured using tools like <code>go test</code> with the <code>-cover</code> flag or third-party libraries such as <code>coverage</code> (now known as <code>gotest</code>). Understanding your test coverage helps you identify gaps in your testing strategy and prioritize which parts of your code need more attention.</p>
</section>
<section id="using-gos-built-in-testing-library-testing.coverage" class="level5" data-number="********.2">
<h5 data-number="********.2" class="anchored" data-anchor-id="using-gos-built-in-testing-library-testing.coverage"><span class="header-section-number">********.2</span> Using Go’s Built-in Testing Library: Testing.Coverage</h5>
<p>Go’s standard library provides comprehensive testing tools, including the <code>testing</code> package and the built-in <code>cover</code> tool. The <code>Testing</code> subdirectory contains packages like:</p>
<ul>
<li><code>out</code> for writing test output to disk.</li>
<li><code>cover</code> for collecting coverage information (though this is deprecated in favor of third-party tools).</li>
<li><code>mock</code> for mocking dependencies.</li>
</ul>
<p>To enable test coverage, you can run:</p>
<div class="sourceCode" id="cb7"><pre class="sourceCode bash code-with-copy"><code class="sourceCode bash"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="ex">go</span> test <span class="at">-cover</span></span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<p>The <code>-cover</code> flag outputs a coverage report detailing which parts of your code were tested and uncovered. This helps you identify areas that need additional testing or refactoring.</p>
</section>
<section id="third-party-tools-for-measuring-and-improving-test-coverage" class="level5" data-number="********.3">
<h5 data-number="********.3" class="anchored" data-anchor-id="third-party-tools-for-measuring-and-improving-test-coverage"><span class="header-section-number">********.3</span> Third-Party Tools for Measuring and Improving Test Coverage</h5>
<p>While Go’s built-in testing library is powerful, it may not always meet the needs of more complex projects. Third-party tools have emerged as valuable additions to a developer’s testing toolkit:</p>
<ol type="1">
<li><strong><code>coverage</code></strong>: Although deprecated, <code>coverage</code> (now known as <code>gotest</code>) has been widely used for measuring test coverage in Go applications.</li>
<li><strong><code>lgtm</code></strong>: A tool that detects potential bugs and inconsistencies in your codebase based on test coverage insights.</li>
<li><strong><code>covd</code></strong>: A command-line tool specifically designed to report test coverage statistics from your Go projects.</li>
</ol>
<p>By integrating these tools into your workflow, you can gain deeper insights into your code’s test coverage and make data-driven decisions about where to focus your testing efforts.</p>
<hr>
</section>
</section>
</section>
<section id="conclusion-1" class="level3" data-number="13.7.2">
<h3 data-number="13.7.2" class="anchored" data-anchor-id="conclusion-1"><span class="header-section-number">13.7.2</span> Conclusion</h3>
<p>Writing tests is a critical part of the software development process. By leveraging test data and mock objects effectively, you can isolate components of your application and ensure their correct behavior. Additionally, monitoring test coverage allows you to identify gaps in your testing strategy and improve overall code quality. With Go’s robust testing framework and a variety of tools available, you can write comprehensive and reliable tests that drive the evolution of your applications.</p>
<p>By following best practices in test data management, mock usage, and test coverage analysis, you will be well-equipped to ensure the reliability and maintainability of your Go applications.</p>


</section>
</section>

</main> <!-- /main -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
<nav class="page-navigation">
  <div class="nav-page nav-page-previous">
      <a href="../../parts/error-handling-and-testing/learn-how-to-handle-errors-in-go.html" class="pagination-link" aria-label="Overview of Error Handling in Go">
        <i class="bi bi-arrow-left-short"></i> <span class="nav-page-text"><span class="chapter-number">12</span>&nbsp; <span class="chapter-title">Overview of Error Handling in Go</span></span>
      </a>          
  </div>
  <div class="nav-page nav-page-next">
      <a href="../../parts/advanced-topics/intro.html" class="pagination-link" aria-label="Advanced Topics">
        <span class="nav-page-text">Advanced Topics</span> <i class="bi bi-arrow-right-short"></i>
      </a>
  </div>
</nav>
</div> <!-- /content -->




</body></html>